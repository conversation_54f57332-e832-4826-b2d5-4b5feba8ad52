"""Add tag_relation table

Revision ID: 8a7b9c6d5e4f
Revises: 7826ab40b532
Create Date: 2024-01-01 00:00:00.000000

"""
from typing import Sequence, Union
from datetime import datetime

from alembic import op
import sqlalchemy as sa
from open_webui.migrations.util import get_revision_id, get_existing_tables

# revision identifiers, used by Alembic.
revision: str = '5d464ac8de32'
down_revision: Union[str, None] = '9f0c9cd09105'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    existing_tables = get_existing_tables()

    if "tag_relation" not in existing_tables:
        op.create_table(
            "tag_relation",
            sa.Column("id", sa.String(), primary_key=True),
            sa.Column("user_id", sa.String(), nullable=False),
            sa.Column("created_at", sa.<PERSON>nteger(), nullable=False),
            sa.Column("updated_at", sa.<PERSON>ger(), nullable=False),
            sa.Column("meta", sa.JSON(), nullable=True),
            sa.Column("table_name", sa.String(), nullable=False),
            sa.Column("table_id", sa.String(), nullable=False),
            sa.Column("tag_id", sa.String(), nullable=False),
            sa.Column("access_control", sa.JSON(), nullable=True),
            sa.PrimaryKeyConstraint("id"),
            sa.UniqueConstraint("table_name", "table_id", "tag_id", name="uq_table_id_tag_id")
        )


def downgrade() -> None:
    op.drop_table("tag_relation")
