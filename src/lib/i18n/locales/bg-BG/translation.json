{"-1 for no limit, or a positive integer for a specific limit": "-1 за липса на ограничение или положително цяло число за определено ограничение.", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' или '-1' за неограничен срок.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(напр. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(напр. `sh webui.sh --api`)", "(latest)": "(последна)", "(leave blank for to use commercial endpoint)": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} Отговори", "{{user}}'s Chats": "{{user}}'s чатове", "{{webUIName}} Backend Required": "{{webUIName}} Изисква се Бекенд", "*Prompt node ID(s) are required for image generation": "*Идентификатор(ите) на възел-а се изисква(т) за генериране на изображения", "A new version (v{{LATEST_VERSION}}) is now available.": "Вече е налична нова версия (v{{LATEST_VERSION}}).", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Моделът на задачите се използва при изпълнението на задачите като генериране на заглавия за чатове и заявки за търсене в мрежата", "a user": "потребител", "About": "Относно", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Достъп", "Access Control": "Контрол на достъпа", "Accessible to all users": "Достъпно за всички потребители", "Account": "<PERSON>ка<PERSON><PERSON>т", "Account Activation Pending": "Активирането на акаунта е в процес на изчакване", "Accurate information": "Точна информация", "Actions": "Действия", "Activate": "Активи<PERSON><PERSON><PERSON>е", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Активирайте тази команда, като въведете \"/{{COMMAND}}\" в полето за чат.", "Active Users": "Активни потребители", "Add": "Добавяне", "Add a model ID": "Добавете ID на модела", "Add a short description about what this model does": "Добавете кратко описание за това какво прави този модел", "Add a tag": "Добавяне на таг", "Add Arena Model": "Добавяне на Arena модел", "Add Connection": "Добавяне на връзка", "Add Content": "Добавяне на съдържание", "Add content here": "Добавете съдържание тук", "Add Custom Parameter": "", "Add custom prompt": "Добавяне на собствен промпт", "Add Files": "Добавяне на Файлове", "Add Group": "Добавяне на група", "Add Memory": "Добавяне на Памет", "Add Model": "Добавяне на Модел", "Add Reaction": "Добавяне на реакция", "Add Tag": "Добавяне на таг", "Add Tags": "Добавяне на тагове", "Add text content": "Добавяне на текстово съдържание", "Add User": "Добавяне на потребител", "Add User Group": "Добавяне на потребителска група", "Adjusting these settings will apply changes universally to all users.": "При промяна на тези настройки промените се прилагат за всички потребители.", "admin": "админ", "Admin": "Администратор", "Admin Panel": "Панел на Администратор", "Admin Settings": "Настройки на администратора", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Администраторите имат достъп до всички инструменти по всяко време; потребителите се нуждаят от инструменти, присвоени за всеки модел в работното пространство.", "Advanced Parameters": "Разширени параметри", "Advanced Params": "Разширени параметри", "All": "", "All Documents": "Всички Документи", "All models deleted successfully": "Всички модели са изтрити успешно", "Allow Call": "", "Allow Chat Controls": "Разреши контроли на чата", "Allow Chat Delete": "Разреши изтриване на чат", "Allow Chat Deletion": "Позволи Изтриване на Чат", "Allow Chat Edit": "Разреши редактиране на чат", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Разреши качване на файлове", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Разреши нелокални гласове", "Allow Speech to Text": "", "Allow Temporary Chat": "Разреши временен чат", "Allow Text to Speech": "", "Allow User Location": "Разреши местоположението на потребителя", "Allow Voice Interruption in Call": "Разреши прекъсване на гласа по време на разговор", "Allowed Endpoints": "Разрешени крайни точки", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Вече имате акаунт?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "<PERSON>и<PERSON><PERSON>и", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "Невероятно", "an assistant": "асистент", "Analyzed": "Ана<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "Анализи<PERSON><PERSON><PERSON><PERSON>...", "and": "и", "and {{COUNT}} more": "и още {{COUNT}}", "and create a new shared link.": "и създай нов общ линк.", "Android": "", "API": "", "API Base URL": "API Базов URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "<PERSON> Ключ", "API Key created.": "API Ключ създаден.", "API Key Endpoint Restrictions": "Ограничения на крайните точки за API Ключ", "API keys": "API Ключове", "API Version": "", "Application DN": "DN на приложението", "Application DN Password": "Парола за DN на приложението", "applies to all users with the \"user\" role": "прилага се за всички потребители с роля \"потребител\"", "April": "<PERSON><PERSON>рил", "Archive": "Архиви<PERSON><PERSON><PERSON><PERSON> Чатове", "Archive All Chats": "Архиви<PERSON><PERSON>й Всички чатове", "Archived Chats": "Архиви<PERSON><PERSON><PERSON><PERSON> Чатове", "archived-chat-export": "експорт-на-архивирани-чатове", "Are you sure you want to clear all memories? This action cannot be undone.": "Сигурни ли сте, че исткате да изчистите всички спомени? Това е необратимо.", "Are you sure you want to delete this channel?": "Сигурни ли сте, че искате да изтриете този канал?", "Are you sure you want to delete this message?": "Сигурни ли сте, че искате да изтриете това съобщение?", "Are you sure you want to unarchive all archived chats?": "Сигурни ли сте, че искате да разархивирате всички архивирани чатове?", "Are you sure?": "Сигурни ли сте?", "Arena Models": "Ар<PERSON>на Модели", "Artifacts": "Артефакти", "Ask": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ask a question": "Задайте въпрос", "Assistant": "Асис<PERSON><PERSON><PERSON>т", "Attach file from knowledge": "", "Attention to detail": "Внимание към детайлите", "Attribute for Mail": "Атрибут за поща", "Attribute for Username": "Атрибут за потребителско име", "Audio": "Аудио", "August": "Август", "Auth": "", "Authenticate": "Удостоверяване", "Authentication": "Автентикация", "Auto": "Авто", "Auto-Copy Response to Clipboard": "Автоматично копиране на отговор в клипборда", "Auto-playback response": "Автоматично възпроизвеждане на отговора", "Autocomplete Generation": "Генериране на автоматично довършване", "Autocomplete Generation Input Max Length": "Максимална дължина на входа за генериране на автоматично довършване", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth низ", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Базов URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Базов URL е задължителен.", "Available list": "Наличен списък", "Available Tools": "Налични инструменти", "available!": "наличен!", "Awful": "Ужасно", "Azure AI Speech": "Azure AI Реч", "Azure Region": "Azure Регион", "Back": "Назад", "Bad Response": "Невалиден отговор от API", "Banners": "Банери", "Base Model (From)": "Базов модел (от)", "before": "преди", "Being lazy": "Мързелив е", "Beta": "Бета", "Bing Search V7 Endpoint": "Крайна точка за Bing Search V7", "Bing Search V7 Subscription Key": "Абонаментен ключ за Bing Search V7", "Bocha Search API Key": "API ключ за Bocha Search", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "API ключ за Brave Search", "By {{name}}": "От {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Calendar": "Календар", "Call": "Обаждане", "Call feature is not supported when using Web STT engine": "Функцията за обаждане не се поддържа при използването на Web STT двигател", "Camera": "Камера", "Cancel": "Отказ", "Capabilities": "Възможности", "Capture": "Заснемане", "Capture Audio": "", "Certificate Path": "Път до сертификата", "Change Password": "Промяна на Парола", "Channel Name": "Име на канала", "Channels": "Канали", "Character": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Ограничение на символите за входа на генериране на автоматично довършване", "Chart new frontiers": "Начертайте нови граници", "Chat": "Чат", "Chat Background Image": "Фоново изображение на чата", "Chat Bubble UI": "Потребителски интерфейс за чат балон", "Chat Controls": "Контроли на чата", "Chat direction": "Направление на чата", "Chat Overview": "Преглед на чата", "Chat Permissions": "Разрешения за чат", "Chat Tags Auto-Generation": "Автоматично генериране на тагове за чат", "Chats": "Чатове", "Check Again": "Проверете Още Веднъж", "Check for updates": "Проверка за актуализации", "Checking for updates...": "Проверка за актуализации...", "Choose a model before saving...": "Изберете модел преди запазване...", "Chunk Overlap": "Припокриване на чънкове", "Chunk Size": "Размер на чънк", "Ciphers": "<PERSON>и<PERSON><PERSON>и", "Citation": "Цитат", "Citations": "", "Clear memory": "Изчистване на паметта", "Clear Memory": "", "click here": "натиснете тук", "Click here for filter guides.": "Натиснете тук за ръководства за филтриране.", "Click here for help.": "Натиснете тук за помощ.", "Click here to": "Натиснете тук, за да", "Click here to download user import template file.": "Натиснете тук, за да изтеглите шаблонния файл за импортиране на потребители.", "Click here to learn more about faster-whisper and see the available models.": "Натиснете тук, за да научите повече за по-бърз шепот и да видите наличните модели.", "Click here to see available models.": "Натиснете тук, за да видите наличните модели.", "Click here to select": "Натиснете тук, за да изберете", "Click here to select a csv file.": "Натиснете тук, за да изберете csv файл.", "Click here to select a py file.": "Натиснете тук, за да изберете py файл.", "Click here to upload a workflow.json file.": "Натиснете тук, за да качите workflow.json файл.", "click here.": "натиснете тук.", "Click on the user role button to change a user's role.": "Натиснете върху бутона за промяна на ролята на потребителя.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Разрешението за запис в клипборда е отказано. Моля, проверете настройките на браузъра си, за да предоставите необходимия достъп.", "Clone": "Клонинг", "Clone Chat": "Клониране на чат", "Clone of {{TITLE}}": "Клонинг на {{TITLE}}", "Close": "Затвори", "Close modal": "", "Close settings modal": "", "Code execution": "Изпълнение на код", "Code Execution": "Изпълнение на код", "Code Execution Engine": "Двигател за изпълнение на кода", "Code Execution Timeout": "", "Code formatted successfully": "Кодът е форматиран успешно", "Code Interpreter": "Интерпретатор на код", "Code Interpreter Engine": "Двигател на интерпретатора на кода", "Code Interpreter Prompt Template": "Шаблон за промпт на интерпретатора на кода", "Collapse": "", "Collection": "Колекция", "Color": "Цвят", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API Ключ", "ComfyUI Base URL": "ComfyUI Базов URL", "ComfyUI Base URL is required.": "ComfyUI Базов URL е задължителен.", "ComfyUI Workflow": "ComfyUI Работен поток", "ComfyUI Workflow Nodes": "Възли на ComfyUI работен поток", "Command": "Команда", "Completions": "Довършвания", "Concurrent Requests": "Едновременни заявки", "Configure": "Конфигуриране", "Confirm": "Потвърди", "Confirm Password": "Потвърди Парола", "Confirm your action": "Потвърдете действието си", "Confirm your new password": "Потвърдете новата си парола", "Connect to your own OpenAI compatible API endpoints.": "Свържете се със собствени крайни точки на API, съвместими с OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Връзки", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Свържете се с администратор за достъп до WebUI", "Content": "Съдържание", "Content Extraction Engine": "", "Continue Response": "Продължи отговора", "Continue with {{provider}}": "Продължете с {{provider}}", "Continue with Email": "Продължете с имейл", "Continue with LDAP": "Продължете с LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Контролирайте как текстът на съобщението се разделя за TTS заявки. 'Пунктуация' разделя на изречения, 'параграфи' разделя на параграфи, а 'нищо' запазва съобщението като един низ.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "Контроли", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Копирано", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Копирана е връзката за споделен чат в клипборда!", "Copied to clipboard": "Копирано в клипборда", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "Копиране на последен код блок", "Copy last response": "Копиране на последен отговор", "Copy Link": "Копиране на връзка", "Copy to clipboard": "Копиране в клипборда", "Copying to clipboard was successful!": "Копирането в клипборда беше успешно!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS трябва да бъде правилно конфигуриран от доставчика, за да позволи заявки от Open WebUI.", "Create": "Създай", "Create a knowledge base": "Създаване на база знания", "Create a model": "Създаване на модел", "Create Account": "Създаване на Акаунт", "Create Admin Account": "Създаване на администраторски акаунт", "Create Channel": "Създаване на канал", "Create Group": "Създаване на група", "Create Knowledge": "Създаване на знания", "Create new key": "Създаване на нов ключ", "Create new secret key": "Създаване на нов секретен ключ", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Създадено на", "Created At": "Създадено на", "Created by": "Създадено от", "CSV Import": "Импортиране на CSV", "Ctrl+Enter to Send": "", "Current Model": "Текущ модел", "Current Password": "Текуща Парола", "Custom": "Персонализ<PERSON><PERSON><PERSON>н", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Тъм<PERSON>н", "Database": "База данни", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "December": "Декември", "Default": "По подразбиране", "Default (Open AI)": "По подразбиране (Open AI)", "Default (SentenceTransformers)": "По подразбиране (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Режимът по подразбиране работи с по-широк набор от модели, като извиква инструменти веднъж преди изпълнение. Нативният режим използва вградените възможности за извикване на инструменти на модела, но изисква моделът да поддържа тази функция по същество.", "Default Model": "Модел по подразбиране", "Default model updated": "Моделът по подразбиране е обновен", "Default Models": "Модели по подразбиране", "Default permissions": "Разрешения по подразбиране", "Default permissions updated successfully": "Разрешенията по подразбиране са успешно актуализирани", "Default Prompt Suggestions": "Промпт Предложения по подразбиране", "Default to 389 or 636 if TLS is enabled": "По подразбиране 389 или 636, ако TLS е активиран", "Default to ALL": "По подразбиране за ВСИЧКИ", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Ролята на потребителя по подразбиране", "Delete": "Изтриване", "Delete a model": "Изтриване на модела", "Delete All Chats": "Изтриване на всички чатове", "Delete All Models": "Изтриване на всички модели", "Delete chat": "Изтриване на чат", "Delete Chat": "Изтриване на Чат", "Delete chat?": "Изтриване на чата?", "Delete folder?": "Изтриване на папката?", "Delete function?": "Изтриване на функцията?", "Delete Message": "Изтриване на съобщение", "Delete message?": "Изтриване на съобщението?", "Delete note?": "", "Delete prompt?": "Изтриване на промпта?", "delete this link": "Изтриване на този линк", "Delete tool?": "Изтриване на инструмента?", "Delete User": "Изтриване на потребител", "Deleted {{deleteModelTag}}": "Изтрито {{deleteModelTag}}", "Deleted {{name}}": "Изтрито {{name}}", "Deleted User": "Изтрит потребител", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Опишете вашата база от знания и цели", "Description": "Описание", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Не следва напълно инструкциите", "Direct": "", "Direct Connections": "Директни връзки", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Директните връзки позволяват на потребителите да се свързват със собствени OpenAI съвместими API крайни точки.", "Direct Connections settings updated": "Настройките за директни връзки са актуализирани", "Direct Tool Servers": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Деактивирано", "Discover a function": "Открийте функция", "Discover a model": "Открийте модел", "Discover a prompt": "Откриване на промпт", "Discover a tool": "Открийте инструмент", "Discover how to use Open WebUI and seek support from the community.": "Открийте как да използвате Open WebUI и потърсете подкрепа от общността.", "Discover wonders": "Открийте чудеса", "Discover, download, and explore custom functions": "Открийте, изтеглете и разгледайте персонализирани функции", "Discover, download, and explore custom prompts": "Откриване, сваляне и преглед на персонализирани промптове", "Discover, download, and explore custom tools": "Открийте, изтеглете и разгледайте персонализирани инструменти", "Discover, download, and explore model presets": "Откриване, сваляне и преглед на пресетове на модели", "Dismissible": "Може да се отхвърли", "Display": "Показване", "Display Emoji in Call": "Показване на емотикони в обаждането", "Display the username instead of You in the Chat": "Показване на потребителското име вместо Вие в чата", "Displays citations in the response": "Показвам цитати в отговора", "Dive into knowledge": "Потопете се в знанието", "Do not install functions from sources you do not fully trust.": "Не инсталирайте функции от източници, на които не се доверявате напълно.", "Do not install tools from sources you do not fully trust.": "Не инсталирайте инструменти от източници, на които не се доверявате напълно.", "Docling": "", "Docling Server URL required.": "", "Document": "Документ", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Документация", "Documents": "Документи", "does not make any external connections, and your data stays securely on your locally hosted server.": "няма външни връзки, а вашите данни остават сигурни на локално назначен сървър.", "Domain Filter List": "Списък с филтри за домейни", "Don't have an account?": "Нямате акаунт?", "don't install random functions from sources you don't trust.": "не инсталирайте случайни функции от източници, на които не се доверявате.", "don't install random tools from sources you don't trust.": "не инсталирайте случайни инструменти от източници, на които не се доверявате.", "Don't like the style": "Не харесваш стила?", "Done": "Готово", "Download": "Изтегляне", "Download as SVG": "Изтегляне като SVG", "Download canceled": "Изтегляне отменено", "Download Database": "Сваляне на база данни", "Drag and drop a file to upload or select a file to view": "Плъзнете и пуснете файл за качване или изберете файл за преглед", "Draw": "Рисуване", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "напр. '30с','10м'. Валидни единици са 'с', 'м', 'ч'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "напр. Филтър за премахване на нецензурни думи от текста", "e.g. en": "", "e.g. My Filter": "напр. Моят филтър", "e.g. My Tools": "напр. Моите инструменти", "e.g. my_filter": "напр. моят_филтър", "e.g. my_tools": "напр. моите_инструменти", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "напр. Инструменти за извършване на различни операции", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "Редактиране", "Edit Arena Model": "Редактиране на Arena модел", "Edit Channel": "Редактиране на канал", "Edit Connection": "Редактиране на връзка", "Edit Default Permissions": "Редактиране на разрешения по подразбиране", "Edit Memory": "Редактиране на памет", "Edit User": "Редактиране на потребител", "Edit User Group": "Редактиране на потребителска група", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Embark on adventures": "Отправете се на приключения", "Embedding": "", "Embedding Batch Size": "Размер на партидата за вграждане", "Embedding Model": "Модел за вграждане", "Embedding Model Engine": "Двигател на модела за вграждане", "Embedding model set to \"{{embedding_model}}\"": "Модел за вграждане е настроен на \"{{embedding_model}}\"", "Enable API Key": "Активиране на API", "Enable autocomplete generation for chat messages": "Активиране на автоматично довършване на съобщения в чата", "Enable Code Execution": "", "Enable Code Interpreter": "Активиране на интерпретатор на код", "Enable Community Sharing": "Разрешаване на споделяне в общност", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Активиране на заключване на паметта (mlock), за да се предотврати изваждането на данните на модела от RAM. Тази опция заключва работния набор от страници на модела в RAM, гарантирайки, че няма да бъдат изхвърлени на диска. Това може да помогне за поддържане на производителността, като се избягват грешки в страниците и се осигурява бърз достъп до данните.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Активиране на мапиране на паметта (mmap) за зареждане на данни на модела. Тази опция позволява на системата да използва дисковото пространство като разширение на RAM, третирайки дисковите файлове, сякаш са в RAM. Това може да подобри производителността на модела, като позволява по-бърз достъп до данните. Въпреки това, може да не работи правилно с всички системи и може да консумира значително количество дисково пространство.", "Enable Message Rating": "Активиране на оценяване на съобщения", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Включване на нови регистрации", "Enabled": "Акти<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Уверете се, че вашият CSV файл включва 4 колони в следния ред: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Парола, Роля.", "Enter {{role}} message here": "Въведете съобщение за {{role}} тук", "Enter a detail about yourself for your LLMs to recall": "Въведете подробности за себе си, за да ги запомнят вашите LLMs", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Въведете низ за удостоверяване на API (напр. потребителско_име:парола)", "Enter Application DN": "Въведете DN на приложението", "Enter Application DN Password": "Въведете парола за DN на приложението", "Enter Bing Search V7 Endpoint": "Въведете крайна точка за Bing Search V7", "Enter Bing Search V7 Subscription Key": "Въведете абонаментен ключ за Bing Search V7", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "Въведете API ключ за Bocha Search", "Enter Brave Search API Key": "Въведете API ключ за Brave Search", "Enter certificate path": "Въведете път до сертификата", "Enter CFG Scale (e.g. 7.0)": "Въведете CFG Scale (напр. 7.0)", "Enter Chunk Overlap": "Въведете припокриване на чънкове", "Enter Chunk Size": "Въведете размер на чънк", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Въведете описание", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "Въведете домейни, разделени със запетаи (напр. example.com,site.org)", "Enter Exa API Key": "Въведете API ключ за Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter Github Raw URL": "Въведете URL адрес на Github Raw", "Enter Google PSE API Key": "Въведете API ключ за Google PSE", "Enter Google PSE Engine Id": "Въведете идентификатор на двигателя на Google PSE", "Enter Image Size (e.g. 512x512)": "Въведете размер на изображението (напр. 512x512)", "Enter Jina API Key": "Въведете API ключ за Jina", "Enter Jupyter Password": "Въведете парола за Jupyter", "Enter Jupyter Token": "Въведете токен за Jupyter", "Enter Jupyter URL": "Въведете URL адрес за Jupyter", "Enter Kagi Search API Key": "Въведете API ключ за Kagi Search", "Enter Key Behavior": "", "Enter language codes": "Въведете кодове на езика", "Enter Mistral API Key": "", "Enter Model ID": "Въведете ID на модела", "Enter model tag (e.g. {{modelTag}})": "Въведете таг на модел (напр. {{modelTag}})", "Enter Mojeek Search API Key": "Въведете API ключ за Mojeek Search", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Въведете брой стъпки (напр. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Въведете URL адрес на прокси (напр. **************************:port)", "Enter reasoning effort": "Въведете усилие за разсъждение", "Enter Sampler (e.g. Euler a)": "Въведете семплер (напр. Euler a)", "Enter Scheduler (e.g. Karras)": "Въведете планировчик (напр. <PERSON>)", "Enter Score": "Въведете оценка", "Enter SearchApi API Key": "Въведете API ключ за SearchApi", "Enter SearchApi Engine": "Въведете двигател за SearchApi", "Enter Searxng Query URL": "Въведете URL адрес за заявка на Searxng", "Enter Seed": "Въведете начално число", "Enter SerpApi API Key": "Въведете API ключ за SerpApi", "Enter SerpApi Engine": "Въведете двигател за SerpApi", "Enter Serper API Key": "Въведете API ключ за Serper", "Enter Serply API Key": "Въведете API ключ за Serply", "Enter Serpstack API Key": "Въведете API ключ за Serpstack", "Enter server host": "Въведете хост на сървъра", "Enter server label": "Въведете етикет на сървъра", "Enter server port": "Въведете порт на сървъра", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Въведете стоп последователност", "Enter system prompt": "Въведете системен промпт", "Enter system prompt here": "", "Enter Tavily API Key": "Въведете API ключ за Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Въведете публичния URL адрес на вашия WebUI. Този URL адрес ще бъде използван за генериране на връзки в известията.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Въведете URL адрес на Tika сървър", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Въведете Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Въведете URL (напр. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Въведете URL (напр. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "Въведете текущата си парола", "Enter Your Email": "Въведете имейл", "Enter Your Full Name": "Въведете вашето пълно име", "Enter your message": "Въведете съобщението си", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "Въведете новата си парола", "Enter Your Password": "Въведете вашата парола", "Enter Your Role": "Въведете вашата роля", "Enter Your Username": "Въведете вашето потребителско име", "Enter your webhook URL": "Въведете вашия URL адрес на webhook", "Error": "Грешка", "ERROR": "ГРЕШКА", "Error accessing Google Drive: {{error}}": "Грешка при достъп до Google Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Грешка при качване на файла: {{error}}", "Evaluations": "Оценки", "Exa API Key": "API ключ за Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Пример: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Пример: ВСИЧКИ", "Example: mail": "Пример: поща", "Example: ou=users,dc=foo,dc=example": "Пример: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Пример: sAMAccountName или uid или userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Изключи", "Execute code for analysis": "Изпълнете кода за анализ", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Експериментално", "Explain": "", "Explore the cosmos": "Изследвайте космоса", "Export": "Износ", "Export All Archived Chats": "Износ на всички архивирани чатове", "Export All Chats (All Users)": "Експортване на всички чатове (за всички потребители)", "Export chat (.json)": "Експортиране на чат (.json)", "Export Chats": "Експортване на чатове", "Export Config to JSON File": "Експортиране на конфигурацията в JSON файл", "Export Functions": "Експортиране на функции", "Export Models": "Експортиране на модели", "Export Presets": "Експортиране на предварителни настройки", "Export Prompt Suggestions": "", "Export Prompts": "Експортване на промптове", "Export to CSV": "Експортиране в CSV", "Export Tools": "Експортиране на инструменти", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "Неуспешно добавяне на файл.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Неуспешно създаване на API ключ.", "Failed to delete note": "", "Failed to fetch models": "Неуспешно извличане на модели", "Failed to load file content.": "", "Failed to read clipboard contents": "Грешка при четене на съдържанието от клипборда", "Failed to save connections": "", "Failed to save models configuration": "Неуспешно запазване на конфигурацията на моделите", "Failed to update settings": "Неуспешно актуализиране на настройките", "Failed to upload file.": "Неуспешно качване на файл.", "Features": "Функции", "Features Permissions": "Разрешения за функции", "February": "Февруари", "Feedback Details": "", "Feedback History": "История на обратната връзка", "Feedbacks": "Обратни връзки", "Feel free to add specific details": "Не се колебайте да добавите конкретни детайли", "File": "<PERSON>а<PERSON><PERSON>", "File added successfully.": "Файлът е добавен успешно.", "File content updated successfully.": "Съдържанието на файла е актуализирано успешно.", "File Mode": "Файлов режим", "File not found.": "Файл не е намерен.", "File removed successfully.": "Файлът е премахнат успешно.", "File size should not exceed {{maxSize}} MB.": "Размерът на файла не трябва да надвишава {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "Файлът е качен успешно", "Files": "Файлове", "Filter is now globally disabled": "Филтърът вече е глобално деактивиран", "Filter is now globally enabled": "Филтърът вече е глобално активиран", "Filters": "Филтри", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Потвърждаване на отпечатък: Не може да се използва инициализационна буква като аватар. Потребителят се връща към стандартна аватарка.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Плавно предаване на големи части от външния отговор", "Focus chat input": "Фокусиране на чат вход", "Folder deleted successfully": "Папката е изтрита успешно", "Folder name cannot be empty.": "Името на папката не може да бъде празно.", "Folder name updated successfully": "Името на папката е актуализирано успешно", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Следвайте инструкциите перфектно", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Изковете нови пътища", "Form": "Форма", "Format your variables using brackets like this:": "Форматирайте вашите променливи, използвайки скоби като това:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "Режим на пълен контекст", "Function": "Функция", "Function Calling": "Извикване на функция", "Function created successfully": "Функцията е създадена успешно", "Function deleted successfully": "Функцията е изтрита успешно", "Function Description": "Описание на функцията", "Function ID": "ID на функцията", "Function imported successfully": "", "Function is now globally disabled": "Функцията вече е глобално деактивирана", "Function is now globally enabled": "Функцията вече е глобално активирана", "Function Name": "Име на функцията", "Function updated successfully": "Функцията е актуализирана успешно", "Functions": "Функции", "Functions allow arbitrary code execution.": "Функциите позволяват произволно изпълнение на кода.", "Functions imported successfully": "Функциите са импортирани успешно", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "Основни", "Generate": "", "Generate an image": "Генериране на изображение", "Generate Image": "Генериране на изображение", "Generate prompt pair": "", "Generating search query": "Генериране на заявка за търсене", "Generating...": "", "Get started": "Започнете", "Get started with {{WEBUI_NAME}}": "Започнете с {{WEBUI_NAME}}", "Global": "Глобално", "Good Response": "Добър отговор", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API ключ", "Google PSE Engine Id": "Идентификатор на двигателя на Google PSE", "Group created successfully": "Групата е създадена успешно", "Group deleted successfully": "Групата е изтрита успешно", "Group Description": "Описание на групата", "Group Name": "Име на групата", "Group updated successfully": "Групата е актуализирана успешно", "Groups": "Гру<PERSON>и", "Haptic Feedback": "Тактилна обратна връзка", "Hello, {{name}}": "Здра<PERSON><PERSON><PERSON>, {{name}}", "Help": "Помощ", "Help us create the best community leaderboard by sharing your feedback history!": "Помогнете ни да създадем най-добрата класация на общността, като споделите историята на вашата обратна връзка!", "Hex Color": "Hex цвят", "Hex Color - Leave empty for default color": "Hex цвят - Оставете празно за цвят по подразбиране", "Hide": "Скрий", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "Начало", "Host": "Хо<PERSON>т", "How can I help you today?": "Как мога да ви помогна днес?", "How would you rate this response?": "Как бихте оценили този отговор?", "HTML": "", "Hybrid Search": "Хибридно търсене", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Потвърждавам, че съм прочел и разбирам последствията от моето действие. Наясно съм с рисковете, свързани с изпълнението на произволен код, и съм проверил надеждността на източника.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Запалете любопитството", "Image": "Изображение", "Image Compression": "Компресия на изображенията", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Генериране на изображения", "Image Generation (Experimental)": "Генерация на изображения (Експериментално)", "Image Generation Engine": "Двигател за генериране на изображения", "Image Max Compression Size": "Максимален размер на компресия на изображения", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Генериране на промпт за изображения", "Image Prompt Generation Prompt": "Промпт за генериране на промпт за изображения", "Image Settings": "Настройки на изображения", "Images": "Изображения", "Import": "", "Import Chats": "Импортване на чатове", "Import Config from JSON File": "Импортиране на конфигурация от JSON файл", "Import From Link": "", "Import Functions": "Импортиране на функции", "Import Models": "Импортиране на модели", "Import Notes": "", "Import Presets": "Импортиране на предварителни настройки", "Import Prompt Suggestions": "", "Import Prompts": "Импортване на промптове", "Import Tools": "Импортиране на инструменти", "Include": "Включи", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Информация", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Въведете команди", "Install from Github URL": "Инсталиране от URL адреса на Github", "Instant Auto-Send After Voice Transcription": "Незабавно автоматично изпращане след гласова транскрипция", "Integration": "", "Interface": "Интерфейс", "Invalid file content": "", "Invalid file format.": "Невалиден формат на файла.", "Invalid JSON file": "", "Invalid Tag": "Невалиден таг", "is typing...": "пише...", "January": "Яну<PERSON><PERSON>и", "Jina API Key": "API ключ за Jina", "join our Discord for help.": "свържете се с нашия Discord за помощ.", "JSON": "JSON", "JSON Preview": "JSON Преглед", "July": "<PERSON><PERSON><PERSON>", "June": "Юни", "Jupyter Auth": "<PERSON><PERSON><PERSON> удостоверяване", "Jupyter URL": "URL адрес на Jupyter", "JWT Expiration": "JWT изтичане", "JWT Token": "JWT токен", "Kagi Search API Key": "API ключ за Kagi Search", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Клавиши за бърз достъп", "Knowledge": "Знания", "Knowledge Access": "Достъп до знания", "Knowledge created successfully.": "Знанието е създадено успешно.", "Knowledge deleted successfully.": "Знанието е изтрито успешно.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Знанието е нулирано успешно.", "Knowledge updated successfully": "Знанието е актуализирано успешно", "Kokoro.js (Browser)": "Kokoro.js (Бр<PERSON><PERSON><PERSON>ъ<PERSON>)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Етикет", "Landing Page Mode": "Режим на начална страница", "Language": "Език", "Language Locales": "", "Languages": "", "Last Active": "Последни активни", "Last Modified": "Последно модифицирано", "Last reply": "Последен отговор", "LDAP": "LDAP", "LDAP server updated": "LDAP сървърът е актуализиран", "Leaderboard": "Класация", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Оставете празно за неограничено", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Оставете празно, за да включите всички модели или изберете конкретни модели", "Leave empty to use the default prompt, or enter a custom prompt": "Оставете празно, за да използвате промпта по подразбиране, или въведете персонализиран промпт", "Leave model field empty to use the default model.": "Оставете полето за модел празно, за да използвате модела по подразбиране.", "License": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Light": "Светъл", "Listening...": "Слушане...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMs могат да правят грешки. Проверете важните данни.", "Loader": "", "Loading Kokoro.js...": "Зареждане на Kokoro.js...", "Local": "Локално", "Local Task Model": "", "Location access not allowed": "", "Lost": "Изгубено", "LTR": "LTR", "Made by Open WebUI Community": "Направено от OpenWebUI общността", "Make password visible in the user interface": "", "Make sure to enclose them with": "Уверете се, че сте заключени с", "Make sure to export a workflow.json file as API format from ComfyUI.": "Уверете се, че експортирате файл workflow.json като API формат от ComfyUI.", "Manage": "Управление", "Manage Direct Connections": "Управление на директни връзки", "Manage Models": "Управление на моделите", "Manage Ollama": "Управление на Ollama", "Manage Ollama API Connections": "Управление на Ollama API", "Manage OpenAI API Connections": "Управление на OpenAI API", "Manage Pipelines": "Управление на пайплайни", "Manage Tool Servers": "", "March": "Ма<PERSON><PERSON>", "Markdown": "", "Max Speakers": "", "Max Upload Count": "Максима<PERSON>ен брой качвания", "Max Upload Size": "Максимален размер на качване", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Максимум 3 модела могат да бъдат сваляни едновременно. Моля, опитайте отново по-късно.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Мемории достъпни от LLMs ще бъдат показани тук.", "Memory": "Памет", "Memory added successfully": "Паметта е добавена успешно", "Memory cleared successfully": "Паметта е изчистена успешно", "Memory deleted successfully": "Паметта е изтрита успешно", "Memory updated successfully": "Паметта е актуализирана успешно", "Merge Responses": "Обединяване на отговори", "Merged Response": "Обединен отговор", "Message rating should be enabled to use this feature": "Оценяването на съобщения трябва да бъде активирано, за да използвате тази функция", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Съобщенията, които изпращате след създаването на връзката, няма да бъдат споделяни. Потребителите с URL адреса ще могат да видят споделения чат.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON>о<PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Моделът '{{modelName}}' беше успешно свален.", "Model '{{modelTag}}' is already in queue for downloading.": "Моделът '{{modelTag}}' е вече в очакване за сваляне.", "Model {{modelId}} not found": "Моделът {{modelId}} не е намерен", "Model {{modelName}} is not vision capable": "Моделът {{modelName}} не поддържа визуални възможности", "Model {{name}} is now {{status}}": "Моделът {{name}} сега е {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "Моделът приема входни изображения", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Моделът е създаден успешно!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Открит е път до файловата система на модела. За актуализацията се изисква съкратено име на модела, не може да продължи.", "Model Filtering": "Филтриране на модели", "Model ID": "ИД на модел", "Model IDs": "ИД-та на моделите", "Model Name": "Име на модел", "Model not selected": "Не е избран модел", "Model Params": "Параметри на модела", "Model Permissions": "Разрешения на модела", "Model unloaded successfully": "", "Model updated successfully": "Моделът е актуализиран успешно", "Model(s) do not support file upload": "", "Modelfile Content": "Съдържание на модфайл", "Models": "Модели", "Models Access": "Достъп до модели", "Models configuration saved successfully": "Конфигурацията на моделите е запазена успешно", "Models Public Sharing": "Споделяне на моделите публично", "Mojeek Search API Key": "API ключ за Mojeek Search", "more": "още", "More": "Повече", "My Notes": "Моите бележки", "Name": "Име", "Name your knowledge base": "Именувайте вашата база от знания", "Native": "Нативен", "New Chat": "Нов чат", "New Folder": "Нова папка", "New Function": "", "New Note": "Нова бележка", "New Password": "Нова парола", "New Tool": "", "new-channel": "нов-канал", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "Без съдържание", "No content found": "Не е намерено съдържание", "No content found in file.": "Не е намерено съдържание във файла", "No content to speak": "Няма съдържание за изговаряне", "No distance available": "Няма налично разстояние", "No feedbacks found": "Не са намерени обратни връзки", "No file selected": "Не е избран файл", "No groups with access, add a group to grant access": "Няма групи с достъп, добавете група, за да предоставите достъп", "No HTML, CSS, or JavaScript content found.": "Не е намерено HTML, CSS или JavaScript съдържание.", "No inference engine with management support found": "Не е намерен механизъм за извод с поддръжка на управлението", "No knowledge found": "Не са намерени знания", "No memories to clear": "", "No model IDs": "Няма ИД-та на моделите", "No models found": "Не са намерени модели", "No models selected": "Няма избрани модели", "No Notes": "Няма бележки", "No results found": "Няма намерени резултати", "No search query generated": "Не е генерирана заявка за търсене", "No source available": "Няма наличен източник", "No users were found.": "Не са намерени потребители.", "No valves to update": "Няма клапани за актуализиране", "None": "Никой", "Not factually correct": "Не е фактологически правилно", "Not helpful": "Не е полезно", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Забележка: Ако зададете минимален резултат, търсенето ще върне само документи с резултат, по-голям или равен на минималния резултат.", "Notes": "Бележки", "Notification Sound": "Звук за известия", "Notification Webhook": "Webhook за известия", "Notifications": "Известия", "November": "Ноември", "OAuth ID": "ID на OAuth", "October": "Октомври", "Off": "Изкл.", "Okay, Let's Go!": "ОК, Нека започваме!", "OLED Dark": "OLED тъмно", "Ollama": "Ollama", "Ollama API": "API на Ollama", "Ollama API settings updated": "Настройките на Ollama API са актуализирани", "Ollama Version": "Ollama Версия", "On": "<PERSON><PERSON><PERSON>.", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Разрешени са само буквено-цифрови знаци и тирета", "Only alphanumeric characters and hyphens are allowed in the command string.": "Само алфанумерични знаци и тире са разрешени в командния низ.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Само колекциите могат да бъдат редактирани, създайте нова база от знания, за да редактирате/добавяте документи.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Само избрани потребители и групи с разрешение могат да имат достъп", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Изглежда URL адресът е невалиден. Моля, проверете отново и опитайте пак.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Упс! Все още има файлове, които се качват. Моля, изчакайте качването да приключи.", "Oops! There was an error in the previous response.": "Упс! Имаше грешка в предишния отговор.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Използвате неподдържан метод (само фронтенд). Моля, сервирайте WebUI от бекенда.", "Open file": "Отвори файл", "Open in full screen": "Отвори на цял екран", "Open modal to configure connection": "", "Open new chat": "Отвори нов чат", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI използва вътрешно по-бързо-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI използва SpeechT5 и CMU Arctic говорителни вграждания.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Версията на Open WebUI (v{{OPEN_WEBUI_VERSION}}) е по-ниска от необходимата версия (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API на OpenAI", "OpenAI API Config": "OpenAI API конфигурация", "OpenAI API Key is required.": "OpenAI API ключ е задължителен.", "OpenAI API settings updated": "Настройките на OpenAI API са актуализирани", "OpenAI URL/Key required.": "OpenAI URL/Key е задължителен.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "или", "Organize your users": "Организирайте вашите потребители", "Other": "Друго", "OUTPUT": "ИЗХОД", "Output format": "Изходен формат", "Output Format": "", "Overview": "Преглед", "page": "страница", "Paginate": "", "Parameters": "", "Password": "Парола", "Paste Large Text as File": "Поставете голям текст като файл", "PDF document (.pdf)": "PDF документ (.pdf)", "PDF Extract Images (OCR)": "Извличане на изображения от PDF (OCR)", "pending": "в очакване", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Отказан достъп при опит за достъп до медийни устройства", "Permission denied when accessing microphone": "Отказан достъп при опит за достъп до микрофона", "Permission denied when accessing microphone: {{error}}": "Отказан достъп при опит за достъп до микрофона: {{error}}", "Permissions": "Разрешения", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Персонализация", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Зака<PERSON>и", "Pinned": "Закачено", "Pioneer insights": "Пионерски прозрения", "Pipeline deleted successfully": "Пайплайнът е изтрит успешно", "Pipeline downloaded successfully": "Пайплайнът е изтеглен успешно", "Pipelines": "Пайплайни", "Pipelines Not Detected": "Не са открити пайплайни", "Pipelines Valves": "Клапани на пайплайни", "Plain text (.md)": "", "Plain text (.txt)": "Обикновен текст (.txt)", "Playground": "Пле<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Моля, внимателно прегледайте следните предупреждения:", "Please do not close the settings page while loading the model.": "Мол<PERSON>, не затваряйте страницата с настройки, докато моделът се зарежда.", "Please enter a prompt": "Моля, въведете промпт", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "Моля, попълнете всички полета.", "Please select a model first.": "Моля, първо изберете модела.", "Please select a model.": "Моля, изберете модел.", "Please select a reason": "Моля, изберете причина", "Port": "Порт", "Positive attitude": "Позитивно отношение", "Prefix ID": "Префикс ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Префикс ID се използва за избягване на конфликти с други връзки чрез добавяне на префикс към ID-тата на моделите - оставете празно, за да деактивирате", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Предишните 30 дни", "Previous 7 days": "Предишните 7 дни", "Previous message": "", "Private": "", "Profile Image": "Профилна снимка", "Prompt": "Промпт", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Промпт (напр. Кажи ми забавен факт за Римската империя)", "Prompt Autocompletion": "", "Prompt Content": "Съдържание на промпта", "Prompt created successfully": "Промптът е създаден успешно", "Prompt suggestions": "Промпт предложения", "Prompt updated successfully": "Промптът е актуализиран успешно", "Prompts": "Промптове", "Prompts Access": "Достъп до промптове", "Prompts Public Sharing": "Публично споделяне на промптове", "Public": "Публично", "Pull \"{{searchValue}}\" from Ollama.com": "Извади \"{{searchValue}}\" от Ollama.com", "Pull a model from Ollama.com": "Издърпайте модела от Ollama.com", "Query Generation Prompt": "Промпт за генериране на запитвания", "RAG Template": "RAG Шаблон", "Rating": "Оценка", "Re-rank models by topic similarity": "Преоценка на моделите по сходство на темата", "Read": "Четене", "Read Aloud": "Прочети на глас", "Reason": "", "Reasoning Effort": "Усилие за разсъждение", "Record": "Запиши", "Record voice": "Записване на глас", "Redirecting you to Open WebUI Community": "Пренасочване към OpenWebUI общността", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Отнасяйте се към себе си като \"Потребител\" (напр. \"Потребителят учи испански\")", "References from": "Препратки от", "Refused when it shouldn't have": "Отказано, когато не трябва да бъде", "Regenerate": "Регенериране", "Reindex": "Реиндексирай", "Reindex Knowledge Base Vectors": "", "Release Notes": "Бележки по изданието", "Releases": "", "Relevance": "Релевантност", "Relevance Threshold": "", "Remove": "Изтриване", "Remove {{MODELID}} from list.": "", "Remove Model": "Изтриване на модела", "Remove this tag from list": "", "Rename": "Преименуване", "Reorder Models": "Преорганизиране на моделите", "Reply in Thread": "Отговори в тред", "Reranking Engine": "Двигател за пренареждане", "Reranking Model": "Модел за преподреждане", "Reset": "Нулиране", "Reset All Models": "Нулиране на всички модели", "Reset Upload Directory": "Нулиране на директорията за качване", "Reset Vector Storage/Knowledge": "Нулиране на векторното хранилище/знания", "Reset view": "Нулиране на изгледа", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Известията за отговори не могат да бъдат активирани, тъй като разрешенията за уебсайта са отказани. Моля, посетете настройките на вашия браузър, за да дадете необходимия достъп.", "Response splitting": "Разделяне на отговора", "Response Watermark": "", "Result": "Резултат", "Retrieval": "Извличане", "Retrieval Query Generation": "Генериране на заявка за извличане", "Rich Text Input for Chat": "Богат текстов вход за чат", "RK": "RK", "Role": "Роля", "Rosé Pine": "Rose pine", "Rosé Pine Dawn": "Rose pine Dawn", "RTL": "RTL", "Run": "Изпълни", "Running": "Изпълнява се", "Save": "<PERSON>а<PERSON><PERSON><PERSON>", "Save & Create": "Запис & Създаване", "Save & Update": "Запис & Актуализиране", "Save As Copy": "Запиши като копие", "Save Tag": "Запиши таг", "Saved": "Запазено", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Запазването на чат логове директно в хранилището на вашия браузър вече не се поддържа. Моля, отделете малко време, за да изтеглите и изтриете чат логовете си, като щракнете върху бутона по-долу. Не се притеснявайте, можете лесно да импортирате отново чат логовете си в бекенда чрез", "Scroll On Branch Change": "", "Search": "Търси", "Search a model": "Търси модел", "Search Base": "База за търсене", "Search Chats": "Търсене на чатове", "Search Collection": "Търсене в колекция", "Search Filters": "Филтри за търсене", "search for tags": "търсене на тагове", "Search Functions": "Търсене на функции", "Search Knowledge": "Търсене в знания", "Search Models": "Търсене на модели", "Search options": "Опции за търсене", "Search Prompts": "Търси Промптове", "Search Result Count": "Брой резултати от търсенето", "Search the internet": "Търсене в интернет", "Search Tools": "Инструменти за търсене", "SearchApi API Key": "API ключ за SearchApi", "SearchApi Engine": "Двигател на SearchApi", "Searched {{count}} sites": "Претърсени {{count}} сайт", "Searching \"{{searchQuery}}\"": "Търсене на \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Търсене в знанията за \"{{searchQuery}}\"", "Searching the web...": "Търсене в интернет...", "Searxng Query URL": "URL адрес на заявка за търсене в Searxng", "See readme.md for instructions": "Вижте readme.md за инструкции", "See what's new": "Виж какво е новото", "Seed": "Начално число", "Select a base model": "Изберете базов модел", "Select a engine": "Изберете двигател", "Select a function": "Изберете функция", "Select a group": "Изберете група", "Select a model": "Изберете модел", "Select a pipeline": "Изберете пайплайн", "Select a pipeline url": "Избор на URL адрес на канал", "Select a tool": "Изберете инструмент", "Select an auth method": "Изберете метод за удостоверяване", "Select an Ollama instance": "Изберете инстанция на Ollama", "Select Engine": "Изберете двигател", "Select Knowledge": "Изберете знание", "Select only one model to call": "Изберете само един модел за извикване", "Selected model(s) do not support image inputs": "Избраният(те) модел(и) не поддържа въвеждане на изображения", "Semantic distance to query": "Семантично разстояние до заявката", "Send": "Изпрати", "Send a Message": "Изпращане на Съобщение", "Send message": "Изпращане на съобщение", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Септември", "SerpApi API Key": "API ключ за SerpApi", "SerpApi Engine": "Двигател на SerpApi", "Serper API Key": "Serper API ключ", "Serply API Key": "API ключ за Serply", "Serpstack API Key": "Serpstack API ключ", "Server connection verified": "Връзката със сървъра е потвърдена", "Set as default": "Задай по подразбиране", "Set CFG Scale": "Задай CFG мащаб", "Set Default Model": "Задай Модел По Подразбиране", "Set embedding model": "Задай модел за вграждане", "Set embedding model (e.g. {{model}})": "Задай модел за вграждане (напр. {{model}})", "Set Image Size": "Задай Размер на Изображението", "Set reranking model (e.g. {{model}})": "Задай модел за преподреждане (напр. {{model}})", "Set Sampler": "Задай семплер", "Set Scheduler": "Задай планировчик", "Set Steps": "Задай Стъпки", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Задайте броя слоеве, които ще бъдат прехвърлени към GPU. Увеличаването на тази стойност може значително да подобри производителността за модели, оптимизирани за GPU ускорение, но може също да консумира повече енергия и GPU ресурси.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Задайте броя работни нишки, използвани за изчисления. Тази опция контролира колко нишки се използват за едновременна обработка на входящи заявки. Увеличаването на тази стойност може да подобри производителността при високи натоварвания с паралелизъм, но може също така да консумира повече ресурси на CPU.", "Set Voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>лас", "Set whisper model": "Задай модел на шепот", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Задава последователностите за спиране, които да се използват. Когато се срещне този модел, LLM ще спре да генерира текст и ще се върне. Множество модели за спиране могат да бъдат зададени чрез определяне на множество отделни параметри за спиране в моделния файл.", "Settings": "Настройки", "Settings saved successfully!": "Настройките са запазени успешно!", "Share": "Подели", "Share Chat": "Подели Чат", "Share to Open WebUI Community": "Споделете с OpenWebUI Общността", "Sharing Permissions": "Права за споделяне", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Пока<PERSON>и", "Show \"What's New\" modal on login": "Покажи модалния прозорец \"Какво е ново\" при вписване", "Show Admin Details in Account Pending Overlay": "Покажи детайлите на администратора в наслагването на изчакващ акаунт", "Show All": "Покажи всички", "Show Less": "Покажи по-малко", "Show Model": "Покажи модел", "Show shortcuts": "Покажи преки пътища", "Show your support!": "Покажете вашата подкрепа!", "Showcased creativity": "Показана креативност", "Sign in": "Вписване", "Sign in to {{WEBUI_NAME}}": "Впишете се в {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Впишете се в {{WEBUI_NAME}} с LDAP", "Sign Out": "Изход", "Sign up": "Регистрация", "Sign up to {{WEBUI_NAME}}": "Регистрирайте се в {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Вписване в {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Източник", "Speech Playback Speed": "Скорост на възпроизвеждане на речта", "Speech recognition error: {{error}}": "Грешка при разпознаване на речта: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Двигател за преобразуване на реч в текста", "Stop": "Спри", "Stop Generating": "", "Stop Sequence": "Стоп последователност", "Stream Chat Response": "Поточен чат отговор", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT Модел", "STT Settings": "STT Настройки", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Подтиту<PERSON> (напр. за Римска империя)", "Success": "Успех", "Successfully updated.": "Успешно обновено.", "Suggested": "Препоръчано", "Support": "Поддръжка", "Support this plugin:": "Подкрепете този плъгин:", "Supported MIME Types": "", "Sync directory": "Синхронизирай директория", "System": "Система", "System Instructions": "Системни инструкции", "System Prompt": "Системен Промпт", "Tags": "Тагове", "Tags Generation": "Генериране на тагове", "Tags Generation Prompt": "Промпт за генериране на тагове", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "Докоснете за прекъсване", "Task Model": "", "Tasks": "Зада<PERSON>и", "Tavily API Key": "<PERSON><PERSON>", "Tavily Extract Depth": "", "Tell us more:": "Повече информация:", "Temperature": "Температура", "Temporary Chat": "Временен чат", "Text Splitter": "Разделител на текст", "Text-to-Speech": "", "Text-to-Speech Engine": "Двигател за преобразуване на текст в реч", "Thanks for your feedback!": "Благодарим ви за вашия отзив!", "The Application Account DN you bind with for search": "DN на акаунта на приложението, с който се свързвате за търсене", "The base to search for users": "Базата за търсене на потребители", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Разработчиците зад този плъгин са страстни доброволци от общността. Ако намирате този плъгин полезен, моля, обмислете да допринесете за неговото развитие.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Класацията за оценка се базира на рейтинговата система Elo и се обновява в реално време.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP атрибутът, който съответства на имейла, който потребителите използват за вписване.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP атрибутът, който съответства на потребителското име, което потребителите използват за вписване.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Класацията в момента е в бета версия и може да коригираме изчисленията на рейтинга, докато усъвършенстваме алгоритъма.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Максималният размер на файла в MB. Ако размерът на файла надвишава този лимит, файлът няма да бъде качен.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Максималният брой файлове, които могат да се използват едновременно в чата. Ако броят на файловете надвишава този лимит, файловете няма да бъдат качени.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Резултатът трябва да бъде стойност между 0,0 (0%) и 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Тема", "Thinking...": "Мисля...", "This action cannot be undone. Do you wish to continue?": "Това действие не може да бъде отменено. Желаете ли да продължите?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Това гарантира, че ценните ви разговори се запазват сигурно във вашата бекенд база данни. Благодарим ви!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Това е експериментална функция, може да не работи според очакванията и подлежи на промяна по всяко време.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Тази опция ще изтрие всички съществуващи файлове в колекцията и ще ги замени с новокачени файлове.", "This response was generated by \"{{model}}\"": "Този отговор беше генериран от \"{{model}}\"", "This will delete": "Това ще изтрие", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Това ще изтрие <strong>{{NAME}}</strong> и <strong>цялото му съдържание</strong>.", "This will delete all models including custom models": "Това ще изтрие всички модели, включително персонализираните модели", "This will delete all models including custom models and cannot be undone.": "Това ще изтрие всички модели, включително персонализираните модели, и не може да бъде отменено.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Това ще нулира базата знания и ще синхронизира всички файлове. Желаете ли да продължите?", "Thorough explanation": "Подробно обяснение", "Thought for {{DURATION}}": "<PERSON>и<PERSON><PERSON>ил за {{DURATION}}", "Thought for {{DURATION}} seconds": "Ми<PERSON><PERSON>ил за {{DURATION}} секунди", "Tika": "Тика", "Tika Server URL required.": "Изисква се URL адрес на Тика сървъра.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Съвет: Актуализирайте няколко слота за променливи последователно, като натискате клавиша Tab в чат входа след всяка подмяна.", "Title": "Заглавие", "Title (e.g. Tell me a fun fact)": "Заглавие (напр. Кажете ми нещо забавно)", "Title Auto-Generation": "Автоматично генериране на заглавие", "Title cannot be an empty string.": "Заглавието не може да бъде празно.", "Title Generation": "Генериране на заглавие", "Title Generation Prompt": "Промпт за генериране на заглавие", "TLS": "TLS", "To access the available model names for downloading,": "За достъп до наличните имена на моделите за изтегляне,", "To access the GGUF models available for downloading,": "За достъп до наличните GGUF модели за изтегляне,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "За достъп до уеб интерфейса, моля, свържете се с администратора. Администраторите могат да управляват статусите на потребителите от Административния панел.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "За да прикачите база знания тук, първо ги добавете към работното пространство \"Знания\".", "To learn more about available endpoints, visit our documentation.": "За да научите повече за наличните крайни точки, посетете нашата документация.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "За да защитим вашата поверителност, от вашата обратна връзка се споделят само оценки, идентификатори на модели, тагове и метаданни-вашите чат логове остават лични и не са включени.", "To select actions here, add them to the \"Functions\" workspace first.": "За да изберете действия тук, първо ги добавете към работното пространство \"Функции\".", "To select filters here, add them to the \"Functions\" workspace first.": "За да изберете филтри тук, първо ги добавете към работното пространство \"Функции\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "За да изберете инструменти тук, първо ги добавете към работното пространство \"Инструменти\".", "Toast notifications for new updates": "Изскачащи известия за нови актуализации", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "Превключване на настройките", "Toggle sidebar": "Превключване на страничната лента", "Toggle whether current connection is active.": "", "Token": "Токен", "Too verbose": "Прекалено многословно", "Tool created successfully": "Инструментът е създаден успешно", "Tool deleted successfully": "Инструментът е изтрит успешно", "Tool Description": "Описание на инструмента", "Tool ID": "ID на инструмента", "Tool imported successfully": "Инструментът е импортиран успешно", "Tool Name": "Име на инструмента", "Tool Servers": "Сървъри за инструменти", "Tool updated successfully": "Инструментът е актуализиран успешно", "Tools": "Инструменти", "Tools Access": "Достъп до инструменти", "Tools are a function calling system with arbitrary code execution": "Инструментите са система за извикване на функции с произволно изпълнение на код", "Tools Function Calling Prompt": "Промпт за извикване на функциите на инструментите", "Tools have a function calling system that allows arbitrary code execution.": "Инструментите имат система за извикване на функции, която позволява произволно изпълнение на код.", "Tools Public Sharing": "Публично споделяне на инструменти", "Top K": "Топ К", "Top K Reranker": "", "Transformers": "Трансформатори", "Trouble accessing Ollama?": "Проблеми с достъпа до Ollama?", "Trust Proxy Environment": "", "TTS Model": "TTS Модел", "TTS Settings": "TTS Настройки", "TTS Voice": "TTS Глас", "Type": "Вид", "Type Hugging Face Resolve (Download) URL": "Въведете Hugging Face Resolve (Изтегляне) URL", "Uh-oh! There was an issue with the response.": "Ох! Имаше проблем с отговора.", "UI": "Потребителски интерфейс", "Unarchive All": "Разархивирай всички", "Unarchive All Archived Chats": "Разархивир<PERSON>й всички архивирани чатове", "Unarchive Chat": "Разархивирай чат", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Разкрий мистерии", "Unpin": "Откачи", "Unravel secrets": "Разгадай тайни", "Untagged": "Без етикет", "Untitled": "Неозаглавен", "Update": "Актуализи<PERSON>а<PERSON>е", "Update and Copy Link": "Обнови и копирай връзката", "Update for the latest features and improvements.": "Актуализирайте за най-новите функции и подобрения.", "Update password": "Обновяване на парола", "Updated": "Актуа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>о", "Updated at": "Актуализирано на", "Updated At": "Актуализирано на", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Надградете до лицензиран план за разширени възможности, включително персонализирани теми и брандиране, и специализирана поддръжка.", "Upload": "Качване", "Upload a GGUF model": "Качване на GGUF модел", "Upload Audio": "", "Upload directory": "Качване на директория", "Upload files": "Качване на файлове", "Upload Files": "Качване на файлове", "Upload Pipeline": "Качване на конвейер", "Upload Progress": "Прогрес на качването", "URL": "URL", "URL Mode": "URL режим", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Използвайте '#' в полето за въвеждане, за да заредите и включите вашите знания.", "Use Gravatar": "Използвайте Gravatar", "Use groups to group your users and assign permissions.": "Използвайте групи, за да групирате вашите потребители и да присвоите разрешения.", "Use Initials": "Използвайте инициали", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "потребител", "User": "Потребител", "User location successfully retrieved.": "Местоположението на потребителя е успешно извлечено.", "User Webhooks": "", "Username": "Потребителско име", "Users": "Потребители", "Using the default arena model with all models. Click the plus button to add custom models.": "Използване на стандартния арена модел с всички модели. Кликнете бутона плюс, за да добавите персонализирани модели.", "Utilize": "Използване на", "Valid time units:": "Валидни единици за време:", "Valves": "Кла<PERSON>ани", "Valves updated": "Клапаните са актуализирани", "Valves updated successfully": "Клапаните са актуализирани успешно", "variable": "променлива", "variable to have them replaced with clipboard content.": "променлива, за да бъдат заменени със съдържание от клипборда.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Версия", "Version {{selectedVersion}} of {{totalVersions}}": "Версия {{selectedVersion}} от {{totalVersions}}", "View Replies": "Преглед на отговорите", "View Result from **{{NAME}}**": "", "Visibility": "Видимост", "Vision": "", "Voice": "<PERSON><PERSON><PERSON><PERSON>", "Voice Input": "<PERSON><PERSON><PERSON><PERSON><PERSON> вход", "Voice mode": "", "Warning": "Предупреждение", "Warning:": "Предупреждение:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Предупреждение: Активирането на това ще позволи на потребителите да качват произволен код на сървъра.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Предупреждение: Ако актуализирате или промените вашия модел за вграждане, трябва да повторите импортирането на всички документи.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Предупреждение: Изпълнението на Jupyter позволява произволно изпълнение на код, което представлява сериозни рискове за сигурността-продължете с изключително внимание.", "Web": "Уеб", "Web API": "Уеб API", "Web Loader Engine": "", "Web Search": "Търсене в уеб", "Web Search Engine": "Уеб търсачка", "Web Search in Chat": "Уеб търсене в чата", "Web Search Query Generation": "Генериране на заявки за уеб търсене", "Webhook URL": "Уебхук URL", "WebUI Settings": "WebUI Настройки", "WebUI URL": "URL на WebUI", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI ще прави заявки към \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI ще прави заявки към \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Какво се опитвате да постигнете?", "What are you working on?": "Върху какво работите?", "What's New in": "Какво е ново в", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Когато е активирано, моделът ще отговаря на всяко съобщение в чата в реално време, генерирайки отговор веднага щом потребителят изпрати съобщение. Този режим е полезен за приложения за чат на живо, но може да повлияе на производителността на по-бавен хардуер.", "wherever you are": "където и да сте", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Локално)", "Why?": "Защо?", "Widescreen Mode": "Широкоекранен режим", "Won": "Спечелено", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Работно пространство", "Workspace Permissions": "Разрешения за работното пространство", "Write": "Напиши", "Write a prompt suggestion (e.g. Who are you?)": "Напиши предложение за промпт (напр. Кой сте вие?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напиши описание в 50 думи, което обобщава [тема или ключова дума].", "Write something...": "Напишете нещо...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "вчера", "You": "Вие", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Можете да чатите с максимум {{maxCount}} файл(а) наведнъж.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Можете да персонализирате взаимодействията си с LLM-и, като добавите спомени чрез бутона 'Управление' по-долу, правейки ги по-полезни и съобразени с вас.", "You cannot upload an empty file.": "Не можете да качите празен файл.", "You do not have permission to upload files.": "Нямате разрешение да качвате файлове.", "You have no archived conversations.": "Нямате архивирани разговори.", "You have shared this chat": "Вие сте споделили този чат", "You're a helpful assistant.": "Вие сте полезен асистент.", "You're now logged in.": "Сега вие влязохте в системата.", "Your account status is currently pending activation.": "Статусът на вашия акаунт в момента очаква активиране.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Цялата ви вноска ще отиде директно при разработчика на плъгина; Open WebUI не взима никакъв процент. Въпреки това, избраната платформа за финансиране може да има свои собствени такси.", "Youtube": "Youtube", "Youtube Language": "Youtube език", "Youtube Proxy URL": "Youtube Прокси URL"}