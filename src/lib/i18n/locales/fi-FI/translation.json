{"-1 for no limit, or a positive integer for a specific limit": "-1 rajoituksetta tai positiivinen kokonaisluku enimmäismääräksi", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' tai '-1' jottei vanhene.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(esim. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(esim. `sh webui.sh --api`)", "(latest)": "(uusin)", "(leave blank for to use commercial endpoint)": "(<PERSON><PERSON><PERSON>, jos haluat käyttää kaupallista päätepistettä)", "{{ models }}": "{{ mallit }}", "{{COUNT}} Available Tools": "{{COUNT}} työkalua saatavilla", "{{COUNT}} hidden lines": "{{COUNT}} piilotettua riviä", "{{COUNT}} Replies": "{{COUNT}} vastausta", "{{user}}'s Chats": "{{user}}:n keskustelut", "{{webUIName}} Backend Required": "{{webUIName}}-backend vaaditaan", "*Prompt node ID(s) are required for image generation": "<PERSON><PERSON> luo<PERSON>en vaaditaan kehote-solmun ID(t)", "A new version (v{{LATEST_VERSION}}) is now available.": "Uusi versio (v{{LATEST_VERSION}}) on nyt saatavilla.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Tehtävämallia käytetään tehtävien suorittamiseen, kuten otsikoiden luomiseen keskusteluille ja verkkohakukyselyille", "a user": "käyttäjä", "About": "Tiet<PERSON>", "Accept autocomplete generation / Jump to prompt variable": "Hyväksy automaattinen tä<PERSON>tö / <PERSON><PERSON><PERSON> kehotteen muuttujaan", "Access": "Pääsy", "Access Control": "Käyttöoikeuksien hallinta", "Accessible to all users": "Käytettävissä kaikille käyttäjille", "Account": "<PERSON><PERSON>", "Account Activation Pending": "Tilin aktivointi odo<PERSON>a", "Accurate information": "Tarkkaa tietoa", "Actions": "<PERSON><PERSON><PERSON><PERSON>", "Activate": "Aktivoi", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivoi tämä komento kirjoittamalla \"/{{COMMAND}}\" chat-syötteeseen.", "Active Users": "Aktiiviset käyttäjät", "Add": "Lisää", "Add a model ID": "<PERSON><PERSON><PERSON><PERSON> mallitunnus", "Add a short description about what this model does": "Lisää lyhyt kuvaus siitä, mitä tämä malli tekee", "Add a tag": "Lisää tagi", "Add Arena Model": "Lisää Areena-malli", "Add Connection": "Lisää yhteys", "Add Content": "Lisää sisältöä", "Add content here": "Lisää sisältöä tähän", "Add Custom Parameter": "Lisää mukautettu parametri", "Add custom prompt": "Lisää mukautettu kehote", "Add Files": "Lisää <PERSON>", "Add Group": "Lisää ryhmä", "Add Memory": "Lisää muistiin", "Add Model": "Lisää malli", "Add Reaction": "Lisää reaktio", "Add Tag": "Lisää tagi", "Add Tags": "Lisää tageja", "Add text content": "Lisää tekstisisältöä", "Add User": "Lisää käyttäjä", "Add User Group": "Lisää käyttäjäryhmä", "Adjusting these settings will apply changes universally to all users.": "Näiden asetusten s<PERSON>äminen vaikuttaa kaikkiin kä<PERSON>äji<PERSON>.", "admin": "hallinta", "Admin": "Ylläpito", "Admin Panel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Admin Settings": "Ylläpitoasetukset", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Ylläpitäjillä on pääsy kaikkiin työkaluihin koko ajan; käyttäjät tarvitsevat työkaluja mallille määritettynä työtilassa.", "Advanced Parameters": "Edistyneet parametrit", "Advanced Params": "Edistyneet parametrit", "All": "<PERSON><PERSON><PERSON>", "All Documents": "<PERSON><PERSON><PERSON>", "All models deleted successfully": "Kai<PERSON><PERSON> mallit poistettu onnistuneesti", "Allow Call": "<PERSON><PERSON> pu<PERSON>ut", "Allow Chat Controls": "<PERSON><PERSON> keskustelu<PERSON>n hallinta", "Allow Chat Delete": "<PERSON><PERSON> kes<PERSON>telu<PERSON>n poisto", "Allow Chat Deletion": "<PERSON><PERSON> kes<PERSON>telu<PERSON>n poisto", "Allow Chat Edit": "<PERSON><PERSON> keskustelujen muokkaus", "Allow Chat Export": "<PERSON><PERSON> keskustelujen vienti", "Allow Chat Share": "<PERSON><PERSON> keskustelujen jako", "Allow Chat System Prompt": "", "Allow File Upload": "<PERSON><PERSON><PERSON><PERSON>", "Allow Multiple Models in Chat": "Salli useampi malli keskustelussa", "Allow non-local voices": "<PERSON><PERSON> ei-paikalliset äänet", "Allow Speech to Text": "<PERSON><PERSON> puhe tekstiksi", "Allow Temporary Chat": "Salli väliaikaiset keskustelut", "Allow Text to Speech": "<PERSON><PERSON> te<PERSON> pu<PERSON>i", "Allow User Location": "<PERSON><PERSON> k<PERSON>j<PERSON><PERSON> sijainti", "Allow Voice Interruption in Call": "<PERSON><PERSON> keskey<PERSON> pu<PERSON>a", "Allowed Endpoints": "Hyväksytyt päätepisteet", "Allowed File Extensions": "Hyväksytyt tiedostomuodot", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Hyväksyty tiedostomuodot. Erittele tiedostomuodot pilkulla. Jätä tyhjäksi kaikille tiedostomuodoille.", "Already have an account?": "<PERSON>ko sinulla jo tili?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "<PERSON><PERSON>", "Always Collapse Code Blocks": "Pienennä aina koodilo<PERSON>kot", "Always Expand Details": "<PERSON><PERSON><PERSON><PERSON> aina tiedot", "Always Play Notification Sound": "Toista aina ilmo<PERSON>", "Amazing": "Hämmästyttävä", "an assistant": "avustaja", "Analyzed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "Ana<PERSON><PERSON><PERSON><PERSON>..", "and": "ja", "and {{COUNT}} more": "ja {{COUNT}} muuta", "and create a new shared link.": "ja luo uusi jaettu link<PERSON>.", "Android": "Android", "API": "API", "API Base URL": "API:n verkko-osoite", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API-avain", "API Key created.": "API-avain luotu.", "API Key Endpoint Restrictions": "API-avaimen päätepiste rajoitukset", "API keys": "API-avaimet", "API Version": "API-versio", "Application DN": "Sovelluksen DN", "Application DN Password": "Sovelluksen DN-salasana", "applies to all users with the \"user\" role": "koskee kaikkia k<PERSON>ä<PERSON>, j<PERSON><PERSON> on \"käyttäj<PERSON>\"-rooli", "April": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Arkisto", "Archive All Chats": "Arkistoi kaikki kes<PERSON>ut", "Archived Chats": "Arkistoidut keskustelut", "archived-chat-export": "arkistoitu-keskustelu-vienti", "Are you sure you want to clear all memories? This action cannot be undone.": "<PERSON><PERSON><PERSON><PERSON> varmasti tyhjentää kaikki muistot? Tätä toimintoa ei voi peruuttaa.", "Are you sure you want to delete this channel?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kanavan?", "Are you sure you want to delete this message?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän viestin?", "Are you sure you want to unarchive all archived chats?": "<PERSON><PERSON><PERSON><PERSON> varmasti purkaa kaikkien arkistoitujen keskustelujen arkistoinnin?", "Are you sure?": "<PERSON><PERSON><PERSON>?", "Arena Models": "Arena-mallit", "Artifacts": "Artefaktit", "Ask": "K<PERSON><PERSON>", "Ask a question": "Kysyä kysymys", "Assistant": "Avustaja", "Attach file from knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Attention to detail": "<PERSON><PERSON><PERSON>", "Attribute for Mail": "Attribuutti sähköpostille", "Attribute for Username": "Attribuutti käyttäjätunnukselle", "Audio": "<PERSON><PERSON><PERSON>", "August": "elokuu", "Auth": "To<PERSON><PERSON>", "Authenticate": "Todentaa", "Authentication": "To<PERSON><PERSON>", "Auto": "Automaattinen", "Auto-Copy Response to Clipboard": "Kopioi vastaus automaattisesti leikepöydälle", "Auto-playback response": "Toista vastaus automaattisesti", "Autocomplete Generation": "Automaattisen täydennyksen luonti", "Autocomplete Generation Input Max Length": "Automaattisen täydennyksen syötteen enimmäispituus", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API:n todennusmerkkijono", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 verkko-osoite", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 verkko-osoite vaaditaan.", "Available list": "Käytettävissä oleva lue<PERSON>lo", "Available Tools": "Käytettävissä olevat työkalut", "available!": "saatavilla!", "Awful": "<PERSON><PERSON><PERSON>", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure-alue", "Back": "<PERSON><PERSON><PERSON>", "Bad Response": "<PERSON><PERSON>", "Banners": "Bannerit", "Base Model (From)": "Perusmalli (alkaen)", "before": "ennen", "Being lazy": "<PERSON><PERSON>", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Search V7 -päätep<PERSON><PERSON> osoite", "Bing Search V7 Subscription Key": "Bing Search V7 -tilauskäyttäjäavain", "Bocha Search API Key": "Bocha Search API -avain", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Docling OCR moottori ja kiele(t) tulee täyttä<PERSON>, tai jättää molemmat tyhjäksi.", "Brave Search API Key": "Brave Search API -avain", "By {{name}}": "<PERSON><PERSON><PERSON><PERSON> {{name}}", "Bypass Embedding and Retrieval": "<PERSON><PERSON> upotus ja haku", "Bypass Web Loader": "<PERSON><PERSON>", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON><PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Puhelutoimintoa ei tueta käytettäessä web-puheentunnistusmoottoria", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Peruuta", "Capabilities": "Ominaisuuksia", "Capture": "Näyttökuva", "Capture Audio": "Kaappaa ääntä", "Certificate Path": "Varmennepolku", "Change Password": "<PERSON><PERSON><PERSON><PERSON>", "Channel Name": "<PERSON><PERSON><PERSON> nimi", "Channels": "Kanavat", "Character": "<PERSON><PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Automaattisen täydennyksen syötteen merkkiraja", "Chart new frontiers": "<PERSON><PERSON><PERSON><PERSON> uus<PERSON> raja<PERSON>", "Chat": "Keskustelu", "Chat Background Image": "<PERSON><PERSON><PERSON><PERSON><PERSON> taustakuva", "Chat Bubble UI": "Keskustelu-pallojen k<PERSON>yttöliittymä", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON><PERSON> hallinta", "Chat direction": "Ke<PERSON><PERSON><PERSON><PERSON> su<PERSON>", "Chat Overview": "Keskustelun yleisk<PERSON>aus", "Chat Permissions": "Keskustelun k<PERSON>öoikeudet", "Chat Tags Auto-Generation": "Keskustelutunnisteiden automaattinen luonti", "Chats": "Keskustelut", "Check Again": "Tarkista uudelleen", "Check for updates": "Tarkista päivitykset", "Checking for updates...": "Tarkistetaan päivityksiä...", "Choose a model before saving...": "Valitse malli ennen tallentamista...", "Chunk Overlap": "Päällekkäisten osien määrä", "Chunk Size": "<PERSON><PERSON><PERSON> koko", "Ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Citation": "Lähdeviite", "Citations": "Lähdeviitteet", "Clear memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> muisti", "Clear Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "click here": "klikkaa tästä", "Click here for filter guides.": "<PERSON><PERSON> k<PERSON> tästä.", "Click here for help.": "Klikkaa tästä saadaksesi apua.", "Click here to": "Klikkaa tästä", "Click here to download user import template file.": "Lataa käyttäjien tuontipohjatiedosto k<PERSON>amalla tästä.", "Click here to learn more about faster-whisper and see the available models.": "Klikkaa tästä oppiaksesi lisää faster-whisperista ja nähdäksesi saatavilla olevat mallit.", "Click here to see available models.": "Klikkaa tästä nädäksesi saatavilla olevat mallit.", "Click here to select": "Klikkaa tästä valitaksesi", "Click here to select a csv file.": "Klikkaa tästä valitaksesi CSV-tiedosto.", "Click here to select a py file.": "Klikkaa tästä valitaksesi py-tiedosto.", "Click here to upload a workflow.json file.": "Klikkaa tästä ladataksesi workflow.json-tiedosto.", "click here.": "klikkaa tästä.", "Click on the user role button to change a user's role.": "Klikkaa käyttäjän roolipainiketta vaihtaaksesi käyttäjän roolia.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Leikepöydälle kirjoitusoikeus evätty. Tarkista selaimesi asetukset ja myönnä tarvittavat käyttöoikeudet.", "Clone": "Kloonaa", "Clone Chat": "Kloonaa keskustelu", "Clone of {{TITLE}}": "{{TITLE}} klooni", "Close": "Sulje", "Close modal": "", "Close settings modal": "<PERSON><PERSON> as<PERSON> modaali", "Code execution": "<PERSON><PERSON><PERSON>tus", "Code Execution": "<PERSON><PERSON><PERSON>", "Code Execution Engine": "<PERSON><PERSON><PERSON>", "Code Execution Timeout": "<PERSON><PERSON><PERSON> a<PERSON>", "Code formatted successfully": "<PERSON><PERSON><PERSON> muoto<PERSON> on<PERSON>", "Code Interpreter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Code Interpreter Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moottori", "Code Interpreter Prompt Template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> keh<PERSON>", "Collapse": "Pienennä", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API -avain", "ComfyUI Base URL": "ComfyUI verkko-osoite", "ComfyUI Base URL is required.": "ComfyUI verkko-osoite vaaditaan.", "ComfyUI Workflow": "ComfyUI-työnkulku", "ComfyUI Workflow Nodes": "ComfyUI-työnkulun solmut", "Command": "Ko<PERSON>", "Completions": "Täydennykset", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Configure": "Määritä", "Confirm": "Vahvista", "Confirm Password": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "Confirm your new password": "Vahvista uusi sa<PERSON>", "Connect to your own OpenAI compatible API endpoints.": "Yhdistä omat OpenAI yhteensopivat API päätepisteet.", "Connect to your own OpenAPI compatible external tool servers.": "Yhdistä omat ulkopuoliset OpenAPI yhteensopivat työkalu palvelimet.", "Connection failed": "<PERSON><PERSON><PERSON><PERSON>", "Connection successful": "<PERSON><PERSON><PERSON><PERSON>", "Connection Type": "Yhteystyyppi", "Connections": "<PERSON><PERSON><PERSON><PERSON>", "Connections saved successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> on<PERSON>", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Ota yhtey<PERSON>ä ylläpitäjään WebUI-käyttöä varten", "Content": "Sisältö", "Content Extraction Engine": "Sisällönpoimintamoottori", "Continue Response": "<PERSON>at<PERSON> vastaus<PERSON>", "Continue with {{provider}}": "<PERSON><PERSON><PERSON> palvelulla {{provider}}", "Continue with Email": "Jatka sähköpostilla", "Continue with LDAP": "Jatka LDAP:illa", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, miten viestin teksti jaetaan puhesynteesipyyntöjä varten. 'Välimerkit' jakaa lause<PERSON>, 'kappa<PERSON><PERSON>' jakaa kappaleisiin ja 'ei mitään' pitää viestin yhtenä merkkijonona.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Kontrolloi yhtenäisyyden ja monimuotoisuuden tasapainoa tuloksessa. Pienempi arvo tuottaa kohdennetumman ja johdonmukai<PERSON>mman tekstin.", "Copied": "Ko<PERSON><PERSON><PERSON>", "Copied link to clipboard": "<PERSON><PERSON> kop<PERSON> le<PERSON>pöydälle", "Copied shared chat URL to clipboard!": "Jaettu keskustelulinkki kopioitu leikepöydälle!", "Copied to clipboard": "<PERSON><PERSON><PERSON><PERSON>öyd<PERSON>", "Copy": "Ko<PERSON>i", "Copy Formatted Text": "<PERSON><PERSON><PERSON> muoto<PERSON>u te<PERSON>ti", "Copy last code block": "Kopioi vii<PERSON><PERSON> k<PERSON>", "Copy last response": "Kopioi vii<PERSON><PERSON> vastaus", "Copy Link": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "Copying to clipboard was successful!": "<PERSON><PERSON>iminen leikepöydälle onnistui!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS täytyy olla konfiguroitu palveluntarjoajan toimesta pyyntöjen hyväksymiseksi Open WebUI:sta.", "Create": "<PERSON><PERSON>", "Create a knowledge base": "<PERSON><PERSON>", "Create a model": "<PERSON>o malli", "Create Account": "<PERSON><PERSON> tili", "Create Admin Account": "<PERSON><PERSON>", "Create Channel": "<PERSON><PERSON> kanava", "Create Group": "<PERSON><PERSON>", "Create Knowledge": "<PERSON><PERSON> <PERSON><PERSON>", "Create new key": "<PERSON><PERSON> uusi avain", "Create new secret key": "<PERSON><PERSON> uusi salainen avain", "Create Note": "<PERSON><PERSON> mui<PERSON><PERSON>", "Create your first note by clicking on the plus button below.": "<PERSON><PERSON> ensimm<PERSON>inen muistiinpanosi painamalla alla olevaa plus painike<PERSON>.", "Created at": "<PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "Luonut", "CSV Import": "CSV-tuonti", "Ctrl+Enter to Send": "Ctrl+Enter lähettääksesi", "Current Model": "Nykyinen malli", "Current Password": "<PERSON><PERSON><PERSON><PERSON>", "Custom": "Muka<PERSON>ttu", "Custom Parameter Name": "Mukautetun parametrin nimi", "Custom Parameter Value": "Mukautetun parametrin arvo", "Danger Zone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Dark": "Tumma", "Database": "Tietokanta", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "Datalab Marker API-avain vaaditaan.", "December": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default": "<PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "<PERSON><PERSON> (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "<PERSON><PERSON><PERSON><PERSON> toimii laajemman mallivalikoiman kanssa kutsumalla työkaluja kerran ennen suorittamista. Natiivitila hyödyntää mallin sisäänrakennettuja työkalujen kutsumisominaisuuk<PERSON>, mutta ed<PERSON>, että malli tukee tätä ominaisuutta.", "Default Model": "<PERSON><PERSON><PERSON><PERSON>", "Default model updated": "Oletusmal<PERSON> p<PERSON>tty", "Default Models": "<PERSON><PERSON><PERSON><PERSON>", "Default permissions": "Oletuskäyttöoikeudet", "Default permissions updated successfully": "Oletuskäyttöoikeudet päivitetty onnistuneesti", "Default Prompt Suggestions": "Oletuskehotteiden ehdotukset", "Default to 389 or 636 if TLS is enabled": "Oletus 389 tai 636, jos <PERSON> on käytössä", "Default to ALL": "Oletus KAIKKI", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Segment<PERSON>u haku on olet<PERSON>sena kohden<PERSON>ua ja <PERSON>tia sis<PERSON>llön poimimista varten. Tätä suositellaan useimmissa tapau<PERSON>.", "Default User Role": "Oletuskäyttäj<PERSON><PERSON><PERSON>", "Delete": "Poista", "Delete a model": "Poista malli", "Delete All Chats": "Poista kaikki keskus<PERSON>ut", "Delete All Models": "Poista kaikki mallit", "Delete chat": "Poista keskustelu", "Delete Chat": "Poista keskustelu", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän keskustelun?", "Delete folder?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kansion?", "Delete function?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän toim<PERSON>?", "Delete Message": "Poista viesti", "Delete message?": "Poista viesti?", "Delete note?": "Poista muistiinpano?", "Delete prompt?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kehotteen?", "delete this link": "poista täm<PERSON>ki", "Delete tool?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän työkalun?", "Delete User": "Poista käyttäjä", "Deleted {{deleteModelTag}}": "Poistettu {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON> {{nimi}}", "Deleted User": "K<PERSON><PERSON><PERSON>ä<PERSON><PERSON> poistettu", "Deployment names are required for Azure OpenAI": "Azure OpenAI:lle vaaditaan käyttöönottojen nimet", "Describe Pictures in Documents": "Kuva<PERSON> dokumentin kuvia", "Describe your knowledge base and objectives": "<PERSON><PERSON><PERSON> tie<PERSON> ja tavo<PERSON>", "Description": "<PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "Tunnista artefaktit automaattisesti", "Dictate": "<PERSON><PERSON>", "Didn't fully follow instructions": "<PERSON>i no<PERSON> oh<PERSON> t<PERSON>", "Direct": "<PERSON><PERSON>", "Direct Connections": "<PERSON><PERSON><PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Suorat yhteydet mahdollistavat käyttäjien yhdistää omia OpenAI-yhteensopivia API-päätepisteitä.", "Direct Connections settings updated": "Suorien yhteyksien asetukset päiv<PERSON>tty", "Direct Tool Servers": "Suorat työkalu palvelimet", "Disable Image Extraction": "Poista kuvien poiminta käytöstä", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Poista kuvien poiminta käytöstä PDF tiedostoista. Jos LLM on käytössä, kuvat tekstitetään automaattisesti. Oletuksena ei käytössä.", "Disabled": "<PERSON><PERSON> k<PERSON>", "Discover a function": "<PERSON><PERSON><PERSON><PERSON> toiminto", "Discover a model": "<PERSON><PERSON><PERSON> malliin", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON> kehote", "Discover a tool": "Löydä työkalu", "Discover how to use Open WebUI and seek support from the community.": "Tutustu Open WebUI:n käyttöön ja pyydä tukea yhteisöltä.", "Discover wonders": "Löydä i<PERSON>ä asioita", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON>, lataa ja tutki mukautettuja toimint<PERSON>", "Discover, download, and explore custom prompts": "Löydä ja lataa mukautettuja kehotteita", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, lataa ja tutki mukautettuja työkaluja", "Discover, download, and explore model presets": "Löydä ja lataa mallien es<PERSON>etuksia", "Dismissible": "Ohitettavissa", "Display": "Näytä", "Display Emoji in Call": "Näytä hymiöitä puhelussa", "Display the username instead of You in the Chat": "Näytä käyttäjänimi keskustelussa \"Sinä\" -<PERSON><PERSON><PERSON>an", "Displays citations in the response": "Näyttää lähdeviitteet vastauksessa", "Dive into knowledge": "Uppoudu tietoon", "Do not install functions from sources you do not fully trust.": "<PERSON><PERSON><PERSON> asenna toimint<PERSON> l<PERSON>hteistä, joi<PERSON> et luota t<PERSON>.", "Do not install tools from sources you do not fully trust.": "<PERSON><PERSON><PERSON> asenna ty<PERSON>kalu<PERSON> lähteistä, joi<PERSON> et luota tä<PERSON>.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling palvelimen verkko-osoite vaaditaan.", "Document": "Asiakirja", "Document Intelligence": "<PERSON><PERSON><PERSON><PERSON>", "Document Intelligence endpoint and key required.": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ätep<PERSON>e ja avain vaaditaan.", "Documentation": "Dokumentaatio", "Documents": "Asiakirjat", "does not make any external connections, and your data stays securely on your locally hosted server.": "ei tee ulkoisia yhteyksiä, ja tietosi pysyvät turvallisesti paikallisesti isännöidyllä palvelimellasi.", "Domain Filter List": "Verkko-osoitteiden suodatuslista", "Don't have an account?": "<PERSON><PERSON><PERSON> sinulla ole tiliä?", "don't install random functions from sources you don't trust.": "<PERSON><PERSON><PERSON> asenna satunnaisia toimintoja lähteistä, joi<PERSON> et luota.", "don't install random tools from sources you don't trust.": "<PERSON>l<PERSON> asenna satunnaisia työkaluja lähteistä, joi<PERSON> et luota.", "Don't like the style": "En pidä tyylistä", "Done": "Val<PERSON>", "Download": "Lataa", "Download as SVG": "Lataa SVG:nä", "Download canceled": "Lataus peru<PERSON>ttu", "Download Database": "<PERSON><PERSON><PERSON>", "Drag and drop a file to upload or select a file to view": "<PERSON><PERSON><PERSON> ja pudota tiedosto lad<PERSON>i tai valitse tiedosto kats<PERSON>i", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to upload": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> lad<PERSON>", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "esim. '30s', '10m'. Kel<PERSON>iset aikayksiköt ovat 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "esim. \"json\" tai JSON kaava", "e.g. 60": "esim. 60", "e.g. A filter to remove profanity from text": "<PERSON><PERSON><PERSON>, joka poistaa k<PERSON> te<PERSON>", "e.g. en": "esim. en", "e.g. My Filter": "es<PERSON>. <PERSON><PERSON>", "e.g. My Tools": "es<PERSON>. <PERSON><PERSON>", "e.g. my_filter": "esim. oma_suodatin", "e.g. my_tools": "esim. omat_työkalut", "e.g. pdf, docx, txt": "esim. pdf, docx, txt", "e.g. Tools for performing various operations": "esim. työkaluja erilaisten toimenpiteiden suorittamiseen", "e.g., 3, 4, 5 (leave blank for default)": "esim. 3, 4, 5 (<PERSON><PERSON><PERSON><PERSON>, j<PERSON> ha<PERSON><PERSON>)", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "esim. en-<PERSON>,ja-JP (Tyhjäksi jättämällä, automaattinen tunnistus)", "e.g., westus (leave blank for eastus)": "esim. westus (jät<PERSON> t<PERSON>hjäksi eastusta varten)", "e.g.) en,fr,de": "esim.) en,fr,de", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Muokkaa Arena-mallia", "Edit Channel": "<PERSON><PERSON><PERSON><PERSON> kanavaa", "Edit Connection": "Muokkaa yhteyttä", "Edit Default Permissions": "Muokkaa oletuskäyttöoikeuksia", "Edit Memory": "Muokkaa muistia", "Edit User": "Muokkaa käyttäjää", "Edit User Group": "Muokkaa käyttäjäryhmää", "Eject": "Poista", "ElevenLabs": "ElevenLabs", "Email": "Sähköposti", "Embark on adventures": "Lähde seikkailu<PERSON>", "Embedding": "Upot<PERSON>", "Embedding Batch Size": "Upotuksen eräkoko", "Embedding Model": "Upotus<PERSON><PERSON>", "Embedding Model Engine": "Upot<PERSON><PERSON><PERSON> moottori", "Embedding model set to \"{{embedding_model}}\"": "\"{{embedding_model}}\" valittu upot<PERSON><PERSON><PERSON><PERSON>", "Enable API Key": "Ota API -avain k<PERSON>n", "Enable autocomplete generation for chat messages": "Ota automaattinen täydennys käyttöön keskusteluviesteissä", "Enable Code Execution": "<PERSON><PERSON> koodin suoritus k<PERSON>", "Enable Code Interpreter": "<PERSON><PERSON> käyttöön", "Enable Community Sharing": "<PERSON><PERSON>ön jakaminen käyttöön", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Ota Memory Locking (mlock) käyttöön estääksesi mallidatan vaihtamisen pois RAM-muistista. Tämä lukitsee mallin työsivut RAM-muistiin, varmistaen että niitä ei vaihdeta levylle. Tämä voi parantaa suorituskykyä välttämällä sivuvikoja ja varmistamalla nopean tietojen käytön.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Ota Memory Mapping (mmap) käyttöön ladataksesi mallidataa. Tämä vaihtoehto sallii järjestelmän käyttää levytilaa RAM-laajennuksena käsittelemällä levytiedostoja kuin ne olisivat RAM-muistissa. Tämä voi parantaa mallin suorituskykyä sallimalla nopeamman tietojen käytön. Kuitenkin se ei välttämättä toimi oikein kaikissa järjestelmissä ja voi kuluttaa huomattavasti levytilaa.", "Enable Message Rating": "Ota viestiarviointi käyttöön", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "<PERSON><PERSON> uudet rekisteröitymiset", "Enabled": "Käytössä", "Endpoint URL": "Päätepiste verkko-osoite", "Enforce Temporary Chat": "Pakota väliaikaiset keskustelut", "Enhance": "<PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Varmista, että CSV-tiedostossasi on 4 saraketta tässä järjestyksessä: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Salasana, Rooli.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> {{role}}-v<PERSON><PERSON>n", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka <PERSON>M-ohjelmat voivat muistaa", "Enter a title for the pending user info overlay. Leave empty for default.": "Kirjoita infon otsikko odottaville käyttäjille. Käytä oletusta j<PERSON>tämällä tyhjäksi.", "Enter a watermark for the response. Leave empty for none.": "<PERSON><PERSON><PERSON><PERSON> vesileima. Jätä t<PERSON>ks<PERSON>, jos et halua mitään.", "Enter api auth string (e.g. username:password)": "Kirjoita API-todennusmerkkijono (esim. käyttäjätunnus:salasana)", "Enter Application DN": "<PERSON><PERSON><PERSON><PERSON> DN", "Enter Application DN Password": "<PERSON><PERSON><PERSON><PERSON> DN-salas<PERSON>", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON><PERSON>ita <PERSON> Search V7 -päätepisteen osoite", "Enter Bing Search V7 Subscription Key": "<PERSON><PERSON><PERSON>ita <PERSON> Search V7 -tilauskäyttäjäavain", "Enter BM25 Weight": "<PERSON><PERSON><PERSON>ita BM25 painoarvo", "Enter Bocha Search API Key": "<PERSON><PERSON><PERSON>ita Bocha Search API -avain", "Enter Brave Search API Key": "Kirjoita Brave Search API -avain", "Enter certificate path": "<PERSON><PERSON><PERSON><PERSON>", "Enter CFG Scale (e.g. 7.0)": "<PERSON><PERSON><PERSON><PERSON>-mit<PERSON> (esim. 7.0)", "Enter Chunk Overlap": "Syötä osien päällekkäisyys", "Enter Chunk Size": "Syötä osien koko", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Syötä pilkulla erottaen \"token:bias_value\" parit (esim. 5432:100, 413:-100)", "Enter Config in JSON format": "<PERSON><PERSON><PERSON><PERSON> konfiguraatio JSON-muodossa", "Enter content for the pending user info overlay. Leave empty for default.": "<PERSON><PERSON><PERSON><PERSON> odottavien käyttäjien infon tekstisisältö. Käytä oletusta jättämällä tyhjäksi.", "Enter Datalab Marker API Key": "<PERSON><PERSON><PERSON><PERSON>lab Marker API-avain", "Enter description": "<PERSON><PERSON><PERSON><PERSON>", "Enter Docling OCR Engine": "<PERSON><PERSON><PERSON><PERSON> moottori", "Enter Docling OCR Language(s)": "<PERSON><PERSON><PERSON><PERSON> kiel<PERSON>(ä)", "Enter Docling Server URL": "<PERSON><PERSON><PERSON><PERSON> palvelimen verkko-osoite", "Enter Document Intelligence Endpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ätepist<PERSON>", "Enter Document Intelligence Key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Enter domains separated by commas (e.g., example.com,site.org)": "Verkko-osoitteet erotetaan pilkulla (esim. esimerkki.com,sivu.org)", "Enter Exa API Key": "<PERSON><PERSON><PERSON>ita Exa API -avain", "Enter External Document Loader API Key": "<PERSON><PERSON><PERSON><PERSON>n Document Loader API-avain", "Enter External Document Loader URL": "<PERSON><PERSON><PERSON><PERSON>n Document Loader:n verkko-osoite", "Enter External Web Loader API Key": "<PERSON><PERSON><PERSON><PERSON>n Web Loader API-avain", "Enter External Web Loader URL": "<PERSON><PERSON><PERSON><PERSON>n Web Loader:n verkko-osoite", "Enter External Web Search API Key": "<PERSON><PERSON><PERSON><PERSON>n Web Search API avain", "Enter External Web Search URL": "<PERSON><PERSON><PERSON><PERSON>n Web Search verkko-osoite", "Enter Firecrawl API Base URL": "<PERSON><PERSON><PERSON><PERSON> Firecrawl API -verkko-osoite", "Enter Firecrawl API Key": "<PERSON><PERSON><PERSON><PERSON> Firecrawl API-avain", "Enter Github Raw URL": "<PERSON><PERSON><PERSON><PERSON> -verkko-osoite", "Enter Google PSE API Key": "Kirjoita Google PSE API -avain", "Enter Google PSE Engine Id": "Kirjoita Google PSE -moottorin tunnus", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> k<PERSON> (esim. 512x512)", "Enter Jina API Key": "<PERSON><PERSON><PERSON><PERSON> -avain", "Enter Jupyter Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Jupyter Token": "<PERSON><PERSON><PERSON><PERSON>", "Enter Jupyter URL": "<PERSON><PERSON><PERSON><PERSON>ite", "Enter Kagi Search API Key": "<PERSON><PERSON><PERSON>ita <PERSON>gi Search API -avain", "Enter Key Behavior": "Enter näppäimen käyttäytyminen", "Enter language codes": "<PERSON><PERSON><PERSON><PERSON>", "Enter Mistral API Key": "<PERSON><PERSON><PERSON><PERSON>l API-avain", "Enter Model ID": "<PERSON><PERSON><PERSON><PERSON> mall<PERSON>", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON><PERSON> (esim. {{modelTag}})", "Enter Mojeek Search API Key": "<PERSON><PERSON><PERSON><PERSON> Search API -avain", "Enter name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Enter New Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> (esim. 50)", "Enter Perplexity API Key": "Aseta Perplexity API-avain", "Enter Playwright Timeout": "Aseta Playwright <PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter Playwright WebSocket URL": "Aseta Playwright WebSocket-aika<PERSON><PERSON><PERSON>", "Enter proxy URL (e.g. **************************:port)": "<PERSON><PERSON><PERSON><PERSON> välityspalvelimen verkko-osoite (esim. https://käyttäjä:salasana@host:port<PERSON>)", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON><PERSON> (esim. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "Enter Score": "<PERSON><PERSON><PERSON><PERSON>", "Enter SearchApi API Key": "Kirjoita SearchApi API -avain", "Enter SearchApi Engine": "<PERSON><PERSON><PERSON><PERSON>-moot<PERSON><PERSON>", "Enter Searxng Query URL": "<PERSON><PERSON><PERSON><PERSON>-k<PERSON><PERSON><PERSON> ve<PERSON>-osoite", "Enter Seed": "<PERSON><PERSON><PERSON><PERSON>", "Enter SerpApi API Key": "<PERSON><PERSON><PERSON>ita SerpApi API -avain", "Enter SerpApi Engine": "Valitse SerpApi <PERSON>", "Enter Serper API Key": "<PERSON><PERSON><PERSON><PERSON> -avain", "Enter Serply API Key": "<PERSON><PERSON><PERSON><PERSON> Serply API -avain", "Enter Serpstack API Key": "<PERSON><PERSON><PERSON><PERSON> API -avain", "Enter server host": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> isä<PERSON><PERSON><PERSON><PERSON>", "Enter server label": "<PERSON><PERSON><PERSON><PERSON> palvelimen tunniste", "Enter server port": "<PERSON><PERSON><PERSON><PERSON> palvel<PERSON> portti", "Enter Sougou Search API sID": "<PERSON><PERSON><PERSON><PERSON> Sougou Search API sID", "Enter Sougou Search API SK": "<PERSON><PERSON><PERSON><PERSON> Sougou Search API SK", "Enter stop sequence": "<PERSON><PERSON><PERSON><PERSON>", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON>stelmäkehote", "Enter system prompt here": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>stelmäkehote tähän", "Enter Tavily API Key": "<PERSON><PERSON><PERSON><PERSON> -avain", "Enter Tavily Extract Depth": "<PERSON><PERSON><PERSON><PERSON> pominta syvyys", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Kir<PERSON>ita julkinen WebUI verkko-osoitteesi. Verkko-osoitetta käytetään osoitteiden luontiin ilmoituksissa.", "Enter the URL of the function to import": "<PERSON><PERSON><PERSON><PERSON> tuo<PERSON>van toim<PERSON>on verkko-osoite", "Enter the URL to import": "<PERSON><PERSON><PERSON><PERSON> tuo<PERSON> ve<PERSON>-osoite", "Enter Tika Server URL": "<PERSON><PERSON><PERSON><PERSON> URL", "Enter timeout in seconds": "<PERSON><PERSON> a<PERSON> sekunneissa", "Enter to Send": "<PERSON><PERSON> lähettääksesi", "Enter Top K": "<PERSON><PERSON><PERSON><PERSON>", "Enter Top K Reranker": "<PERSON><PERSON><PERSON>ita Top K uudelleen sijoittaja", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON><PERSON><PERSON><PERSON>ite (esim. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "<PERSON><PERSON><PERSON><PERSON>ite (esim. http://localhost:11434)", "Enter Yacy Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "<PERSON><PERSON><PERSON><PERSON> (esim. http://yacy.example.com:8090)", "Enter Yacy Username": "<PERSON><PERSON><PERSON><PERSON>", "Enter your current password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Email": "<PERSON><PERSON><PERSON><PERSON>köpostiosoitteesi", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON> koko ni<PERSON>i", "Enter your message": "<PERSON><PERSON><PERSON><PERSON>", "Enter your name": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "Enter Your Name": "<PERSON><PERSON><PERSON><PERSON>", "Enter your new password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Role": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Username": "<PERSON><PERSON><PERSON><PERSON>", "Enter your webhook URL": "<PERSON><PERSON><PERSON><PERSON>", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "VIRHE", "Error accessing Google Drive: {{error}}": "<PERSON><PERSON><PERSON> y<PERSON>äessä Google Drive: {{error}}", "Error accessing media devices.": "<PERSON><PERSON><PERSON> k<PERSON>ytettäessä.", "Error starting recording.": "<PERSON><PERSON><PERSON>.", "Error unloading model: {{error}}": "<PERSON><PERSON><PERSON> mallia lad<PERSON>: {{error}}", "Error uploading file: {{error}}": "<PERSON><PERSON><PERSON>: {{error}}", "Evaluations": "Arvioinnit", "Exa API Key": "Exa API -avain", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Esimerkki: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Esimerkki: KAIKKI", "Example: mail": "Esimerkki: posti", "Example: ou=users,dc=foo,dc=example": "Esimerkki: ou=käyttäjät,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Esimerkki: sAMAccountName tai uid tai userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Lisenssiisi kuuluva paikkamäärä on ylittynyt. Ota yhteyttä tukeen, jos haluat lisätä paikkoja.", "Exclude": "<PERSON><PERSON><PERSON> pois", "Execute code for analysis": "<PERSON><PERSON><PERSON> koodi analys<PERSON>ia varten", "Executing **{{NAME}}**...": "Suoritetaan **{{NAME}}**...", "Expand": "<PERSON><PERSON><PERSON><PERSON>", "Experimental": "<PERSON><PERSON><PERSON><PERSON>", "Explain": "Selitä", "Explore the cosmos": "<PERSON><PERSON><PERSON>", "Export": "Vie", "Export All Archived Chats": "Vie kaikki arkistoidut keskustelut", "Export All Chats (All Users)": "Vie kaikki keskustelut (kaikki käyttäjät)", "Export chat (.json)": "<PERSON><PERSON> kes<PERSON> (.json)", "Export Chats": "Vie keskustelut", "Export Config to JSON File": "<PERSON><PERSON>-tied<PERSON><PERSON>", "Export Functions": "<PERSON><PERSON>", "Export Models": "<PERSON><PERSON> malleja", "Export Presets": "<PERSON><PERSON>", "Export Prompt Suggestions": "Vie kehote ehdotukset", "Export Prompts": "<PERSON>ie keh<PERSON>et", "Export to CSV": "Vie CSV-<PERSON><PERSON><PERSON>", "Export Tools": "Vie t<PERSON>ökalut", "External": "<PERSON><PERSON><PERSON><PERSON>", "External Document Loader URL required.": "Ulkoisen Document Loader:n verkko-osoite on vaaditaan.", "External Task Model": "Ulkoinen työmalli", "External Web Loader API Key": "Ulkoinen Web Loader API-avain", "External Web Loader URL": "Ulkoinen Web Loader verkko-osoite", "External Web Search API Key": "Ulkoinen Web Search API-avain", "External Web Search URL": "Ulkoinen Web Search verkko-osoite", "Failed to add file.": "Tiedoston lisääminen epäonnistui.", "Failed to connect to {{URL}} OpenAPI tool server": "Yhdistäminen {{URL}} OpenAPI työkalu palvelimeen epäonnistui", "Failed to copy link": "<PERSON><PERSON> kop<PERSON><PERSON><PERSON> e<PERSON>", "Failed to create API Key.": "API-avaimen luonti epäonnistui.", "Failed to delete note": "Muistiinpanon poistaminen ep<PERSON>on<PERSON>ui", "Failed to fetch models": "<PERSON><PERSON> hakeminen ep<PERSON>on<PERSON>", "Failed to load file content.": "Tiedoston sisällön lataaminen epäonnistui.", "Failed to read clipboard contents": "Leikepöydän sisä<PERSON>ön lukeminen epäonnistui", "Failed to save connections": "Yhteyksien tall<PERSON>aminen epäonnistui", "Failed to save models configuration": "<PERSON><PERSON> m<PERSON>ks<PERSON> tallentaminen epäonnistui", "Failed to update settings": "Asetusten päivittäminen epäonnistui", "Failed to upload file.": "Tiedoston lataaminen epäonnistui.", "Features": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Features Permissions": "Ominaisuuksien käyttöoikeudet", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Palautehistoria", "Feedbacks": "Palautteet", "Feel free to add specific details": "Voit lisätä tarkempia tietoja", "File": "Tiedosto", "File added successfully.": "Tiedosto lisätty onnistuneesti.", "File content updated successfully.": "Tiedoston sisältö päivitetty onnistuneesti.", "File Mode": "Tiedostotila", "File not found.": "Tiedostoa ei lö<PERSON>.", "File removed successfully.": "Tiedosto poistettu onnistuneesti.", "File size should not exceed {{maxSize}} MB.": "Tiedoston koko ei saa ylittää {{maxSize}} MB.", "File Upload": "Tiedoston lataus", "File uploaded successfully": "Tiedosto lad<PERSON> onnistuneesti", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter is now globally disabled": "<PERSON><PERSON><PERSON> on nyt poistettu käytöstä globaalisti", "Filter is now globally enabled": "<PERSON><PERSON><PERSON> on nyt otettu k<PERSON>yttöön globaalisti", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Sormenjäljen väärentäminen havaittu: Alkukirjaimia ei voi käyttää avatarina. Käytetään oletusprofiilikuvaa.", "Firecrawl API Base URL": "Firecrawl API -verkko-osoite", "Firecrawl API Key": "Firecrawl API-avain", "Fluidly stream large external response chunks": "Virtaa suuria ulkoisia vast<PERSON><PERSON> j<PERSON>", "Focus chat input": "Fokusoi syöttökenttään", "Folder deleted successfully": "<PERSON><PERSON><PERSON> pois<PERSON> onnist<PERSON>", "Folder name cannot be empty.": "Kansion nimi ei voi olla tyhjä.", "Folder name updated successfully": "Ka<PERSON><PERSON> nimi p<PERSON>iv<PERSON>tty onnistuneesti", "Follow up": "Jatkokysymykset", "Follow Up Generation": "Jatkokysy<PERSON><PERSON> luonti", "Follow Up Generation Prompt": "Jatkokysymysten luonti kehoite", "Follow-Up Auto-Generation": "Jatkokysymysten automaattinen luonti", "Followed instructions perfectly": "<PERSON><PERSON><PERSON> oh<PERSON>", "Force OCR": "Pakota OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Pakota OCR:n käyttö kaikilla PDF-sivuilla. Tämä voi johtaa huonompiin tuloksiin jos PDF:n tekstisisältö on laadukasta. Oletusarvo, ei käytöstä.", "Forge new paths": "Luo uusia polkuja", "Form": "Lomake", "Format your variables using brackets like this:": "Muotoile muuttujasi hakasulkeilla tällä tavalla:", "Forwards system user session credentials to authenticate": "Välittää järjestelmän käyttäjän istunnon tunnistetiedot todennusta varten", "Full Context Mode": "<PERSON><PERSON>", "Function": "<PERSON><PERSON><PERSON><PERSON>", "Function Calling": "<PERSON><PERSON><PERSON><PERSON> kutsu", "Function created successfully": "<PERSON><PERSON><PERSON><PERSON> luotu onnist<PERSON><PERSON>i", "Function deleted successfully": "<PERSON><PERSON><PERSON><PERSON> pois<PERSON>u onnistuneesti", "Function Description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Function ID": "<PERSON><PERSON><PERSON><PERSON> tunnus", "Function imported successfully": "<PERSON><PERSON><PERSON><PERSON> tuonti on<PERSON>ui", "Function is now globally disabled": "<PERSON><PERSON><PERSON><PERSON> on nyt poistettu käytöstä globaalisti", "Function is now globally enabled": "<PERSON><PERSON><PERSON><PERSON> on nyt otettu käyttöön globaalisti", "Function Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Function updated successfully": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> onnistuneesti", "Functions": "<PERSON><PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "<PERSON><PERSON><PERSON><PERSON> sallivat mi<PERSON> koodin su<PERSON>n.", "Functions imported successfully": "<PERSON><PERSON><PERSON><PERSON> tuotu onnist<PERSON><PERSON>i", "Gemini": "Gemini", "Gemini API Config": "Gemini API konfiguraatio", "Gemini API Key is required.": "Gemini API -avain on vaaditaan.", "General": "<PERSON><PERSON><PERSON>", "Generate": "<PERSON><PERSON>", "Generate an image": "<PERSON><PERSON> kuva", "Generate Image": "<PERSON><PERSON> kuva", "Generate prompt pair": "<PERSON><PERSON> keh<PERSON>", "Generating search query": "<PERSON><PERSON><PERSON>", "Generating...": "<PERSON><PERSON><PERSON>...", "Get started": "Aloita", "Get started with {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> {{WEBUI_NAME}}:iä", "Global": "<PERSON><PERSON><PERSON>", "Good Response": "Hyvä <PERSON>aus", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API -avain", "Google PSE Engine Id": "Google PSE -moottorin tunnus", "Group created successfully": "<PERSON><PERSON><PERSON><PERSON> luotu onnistuneesti", "Group deleted successfully": "<PERSON><PERSON><PERSON><PERSON> poistettu onnistuneesti", "Group Description": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Group Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "Group updated successfully": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>iv<PERSON><PERSON> onnistuneesti", "Groups": "<PERSON><PERSON><PERSON><PERSON>", "Haptic Feedback": "<PERSON><PERSON><PERSON> palaute", "Hello, {{name}}": "Hei, {{name}}", "Help": "<PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Auta meitä luomaan paras yhtei<PERSON>ön t<PERSON>lo jaka<PERSON> palautehistoriasi!", "Hex Color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hex Color - Leave empty for default color": "He<PERSON><PERSON>imaaliväri - Jät<PERSON> t<PERSON>j<PERSON><PERSON>, jos haluat o<PERSON>", "Hide": "<PERSON><PERSON><PERSON>", "Hide from Sidebar": "<PERSON><PERSON><PERSON> sivu<PERSON>ta", "Hide Model": "Piilota malli", "High Contrast Mode": "<PERSON><PERSON><PERSON> kontrastin tila", "Home": "<PERSON><PERSON>", "Host": "Palvelin", "How can I help you today?": "Miten voin auttaa sinua tänään?", "How would you rate this response?": "<PERSON>inka arvioisit tätä vastausta?", "HTML": "HTML", "Hybrid Search": "<PERSON><PERSON><PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON><PERSON>, että olen lukenut ja ymmärrän toimintani se<PERSON>. <PERSON>n tietoinen mieli<PERSON>sen koodin suorittamiseen liittyvistä riskeistä ja olen varmistanut lähteen luotettavuuden.", "ID": "<PERSON><PERSON><PERSON>", "iframe Sandbox Allow Forms": "<PERSON>li lo<PERSON>et iframe hiekkalaatikossa", "iframe Sandbox Allow Same Origin": "<PERSON><PERSON>e hiekkalaatikko samasta <PERSON>kuperäst<PERSON>", "Ignite curiosity": "Sytytä uteliaisuus", "Image": "<PERSON><PERSON>", "Image Compression": "<PERSON><PERSON>", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kokeellinen)", "Image Generation Engine": "Kuvagener<PERSON><PERSON>ott<PERSON>", "Image Max Compression Size": "<PERSON><PERSON>", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "<PERSON><PERSON> kehote generointi", "Image Prompt Generation Prompt": "<PERSON><PERSON> gene<PERSON> kehote", "Image Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON>", "Import": "<PERSON><PERSON>", "Import Chats": "<PERSON><PERSON> kes<PERSON>", "Import Config from JSON File": "<PERSON><PERSON>SON-tiedostosta", "Import From Link": "<PERSON><PERSON> ve<PERSON>-o<PERSON><PERSON><PERSON>", "Import Functions": "<PERSON><PERSON>", "Import Models": "<PERSON><PERSON> malleja", "Import Notes": "<PERSON><PERSON> m<PERSON>", "Import Presets": "<PERSON><PERSON>", "Import Prompt Suggestions": "Tuo kehote ehdotukset", "Import Prompts": "<PERSON><PERSON> keh<PERSON>et", "Import Tools": "<PERSON><PERSON>", "Include": "Sisällytä", "Include `--api-auth` flag when running stable-diffusion-webui": "Sisällytä `--api-auth`-lippu ajetta<PERSON>a stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Sisällytä `--api`-lippu ajettaessa stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "<PERSON><PERSON><PERSON>", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Upota koko sisältö kontekstiin katta<PERSON>a käsittelyä varten. Tätä suositellaan monimutkaisille kyselyille.", "Input commands": "Syötekäskyt", "Install from Github URL": "<PERSON><PERSON><PERSON>-URL:stä", "Instant Auto-Send After Voice Transcription": "Heti automaattinen lähetys äänitunnistuksen jälkeen", "Integration": "Integrointi", "Interface": "Käyttöliittymä", "Invalid file content": "Vir<PERSON>llinen tiedostosisältö", "Invalid file format.": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON>.", "Invalid JSON file": "<PERSON><PERSON><PERSON>llinen JSON tiedosto", "Invalid Tag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tagi", "is typing...": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "January": "tammikuu", "Jina API Key": "Jina API -avain", "join our Discord for help.": "liity <PERSON>rdiimme sa<PERSON> a<PERSON>a.", "JSON": "JSON", "JSON Preview": "JSON-esikatselu", "July": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "June": "kesä<PERSON>u", "Jupyter Auth": "<PERSON><PERSON><PERSON> to<PERSON>", "Jupyter URL": "Jupyter verk<PERSON>-osoite", "JWT Expiration": "JWT-vanheneminen", "JWT Token": "JWT-token", "Kagi Search API Key": "Kagi Search API -avain", "Keep in Sidebar": "Pidä si<PERSON>", "Key": "Avain", "Keyboard shortcuts": "Pikanäppäimet", "Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON>", "Knowledge created successfully.": "Tietokanta luotu onnistuneesti.", "Knowledge deleted successfully.": "Tietokanta poistettu onnistuneesti.", "Knowledge Public Sharing": "Tietokannan julkinen jakaminen", "Knowledge reset successfully.": "Tietokanta nollattu onnistuneesti.", "Knowledge updated successfully": "Tietokanta päivitetty onnistuneesti", "Kokoro.js (Browser)": "Kokoro.js (selain)", "Kokoro.js Dtype": "", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Landing Page Mode": "<PERSON><PERSON><PERSON><PERSON> tila", "Language": "<PERSON><PERSON>", "Language Locales": "Kielialueet", "Languages": "<PERSON><PERSON>", "Last Active": "Viimeksi aktiivinen", "Last Modified": "<PERSON>iimeks<PERSON> muo<PERSON>", "Last reply": "<PERSON><PERSON><PERSON><PERSON><PERSON> vast<PERSON>u", "LDAP": "LDAP", "LDAP server updated": "LDAP-pal<PERSON><PERSON> p<PERSON>", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Learn more about OpenAPI tool servers.": "Lue lisää OpenAPI työkalu palvelimista.", "Leave empty for no compression": "", "Leave empty for unlimited": "<PERSON><PERSON> t<PERSON>", "Leave empty to include all models from \"{{url}}\" endpoint": "Jät<PERSON> tyhjäksi sisällyttääksesi \"{{url}}\" päätepisteen mallit", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Jätä tyhjäksi sisällyttääksesi \"{{url}}/api/tags\" päätepisteen mallit", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Jät<PERSON> tyhjäksi sisällyttääksesi \"{{url}}/models\" päätepisteen mallit", "Leave empty to include all models or select specific models": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>, jos haluat sisällyttää kaikki mallit tai valitse tietyt mallit", "Leave empty to use the default prompt, or enter a custom prompt": "Jätä tyhjäksi käyttääksesi oletuskehotetta tai kirjoita mukautettu kehote", "Leave model field empty to use the default model.": "Jätä malli kenttä tyhjäksi käyttääks<PERSON> oletus mallia.", "License": "<PERSON><PERSON><PERSON>", "Light": "Vaalea", "Listening...": "<PERSON><PERSON><PERSON><PERSON>...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Kielimallit voivat tehdä virheitä. Tarkista tärkeät tiedot.", "Loader": "<PERSON><PERSON><PERSON>", "Loading Kokoro.js...": "Ladataan Kokoro.js...", "Local": "<PERSON><PERSON><PERSON><PERSON>", "Local Task Model": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Location access not allowed": "Ei pääsyä sijaintitietoihin", "Lost": "Men<PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Tehnyt OpenWebUI-yhteisö", "Make password visible in the user interface": "", "Make sure to enclose them with": "Varmista, että suljet ne", "Make sure to export a workflow.json file as API format from ComfyUI.": "Muista viedä workflow.json-tiedosto API-muodossa ComfyUI:sta.", "Manage": "Hallitse", "Manage Direct Connections": "Hallitse suoria yhteyksiä", "Manage Models": "<PERSON>itse malleja", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Hallitse Ollama API -yhteyksiä", "Manage OpenAI API Connections": "Hallitse OpenAI API -yhteyksiä", "Manage Pipelines": "<PERSON><PERSON><PERSON>", "Manage Tool Servers": "Hallitse työkalu palvelimia", "March": "ma<PERSON><PERSON><PERSON>", "Markdown": "<PERSON><PERSON>", "Max Speakers": "Puhujien enimmäismäärä", "Max Upload Count": "Latausten enimmäismäärä", "Max Upload Size": "Latausten enimmäiskoko", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Enintään 3 mallia voidaan ladata samanaikaisesti. Yritä myöhemmin uudelleen.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> käyttävät, näkyvät tässä.", "Memory": "<PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON><PERSON><PERSON> l<PERSON>nistuneesti", "Memory cleared successfully": "<PERSON><PERSON><PERSON> onnistuneesti", "Memory deleted successfully": "<PERSON><PERSON><PERSON> pois<PERSON>ttu onnist<PERSON>i", "Memory updated successfully": "<PERSON><PERSON><PERSON> p<PERSON>iv<PERSON>tty onnistuneesti", "Merge Responses": "Yhdistä vastaukset", "Merged Response": "<PERSON><PERSON><PERSON><PERSON> vastaus", "Message rating should be enabled to use this feature": "Tämän toiminnon käyttämiseksi viestiarviointi on otettava käyttöön", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Linkin luomisen jälkeen lähettämäsi viestit eivät ole jaettuja. Käyttäjät, j<PERSON><PERSON> on verkko-osoite, voivat tarkastella jaettua keskustelua.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "Mistral OCR api-avain vaaditaan", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON>i '{{modelName}}' ladattiin onnist<PERSON>esti.", "Model '{{modelTag}}' is already in queue for downloading.": "<PERSON><PERSON> '{{modelTag}}' on jo jono<PERSON>.", "Model {{modelId}} not found": "<PERSON><PERSON> {{modelId}} ei l<PERSON>yt", "Model {{modelName}} is not vision capable": "Malli {{modelName}} ei kykene n<PERSON>yn", "Model {{name}} is now {{status}}": "Malli {{name}} on nyt {{status}}", "Model {{name}} is now hidden": "Malli {{name}} on nyt piilotettu", "Model {{name}} is now visible": "<PERSON><PERSON> {{name}} on nyt näkyvissä", "Model accepts file inputs": "Malli hyväksyy tiedostosyötteet", "Model accepts image inputs": "Malli hyväksyy kuvasyötteitä", "Model can execute code and perform calculations": "Malli voi suorittaa koodia ja laskelmia", "Model can generate images based on text prompts": "Malli voi luoda kuvia tekstikehotteiden perusteella", "Model can search the web for information": "Malli voi hakea tietoa verkosta", "Model created successfully!": "<PERSON>i luotu onnistuneesti!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON><PERSON>j<PERSON>rjestelmäpolku havaittu. <PERSON><PERSON> lyhytnimi vaadit<PERSON> p<PERSON>, ei voida jatkaa.", "Model Filtering": "<PERSON><PERSON> suodatus", "Model ID": "<PERSON><PERSON> tunnus", "Model IDs": "Mallitunnukset", "Model Name": "<PERSON><PERSON> nimi", "Model not selected": "Mallia ei ole valittu", "Model Params": "<PERSON>in parametrit", "Model Permissions": "<PERSON><PERSON>", "Model unloaded successfully": "<PERSON>i purettu onnistuneesti", "Model updated successfully": "<PERSON><PERSON> p<PERSON> onnistuneesti", "Model(s) do not support file upload": "<PERSON><PERSON>(t) ei tue tiedostojen <PERSON>", "Modelfile Content": "Mallitiedoston sisältö", "Models": "Mallit", "Models Access": "<PERSON><PERSON>", "Models configuration saved successfully": "<PERSON><PERSON> m<PERSON>ritykset tallennettu onnistuneesti", "Models Public Sharing": "<PERSON><PERSON> jul<PERSON>en jaka<PERSON>n", "Mojeek Search API Key": "Mojeek Search API -avain", "more": "lisää", "More": "Lisää", "My Notes": "<PERSON><PERSON> m<PERSON>", "Name": "<PERSON><PERSON>", "Name your knowledge base": "<PERSON>", "Native": "Natiivi", "New Chat": "Uusi keskustelu", "New Folder": "<PERSON><PERSON><PERSON> kansio", "New Function": "<PERSON><PERSON><PERSON> toim<PERSON>o", "New Note": "<PERSON><PERSON><PERSON> muistii<PERSON><PERSON>o", "New Password": "<PERSON><PERSON><PERSON>", "New Tool": "Uusi t<PERSON>ökal<PERSON>", "new-channel": "uusi-kanava", "Next message": "<PERSON><PERSON><PERSON> viesti", "No chats found for this user.": "Käyttäjän keskusteluja ei löytynyt.", "No chats found.": "Keskusteluja ei löytynyt", "No content": "<PERSON>i sis<PERSON>", "No content found": "Sisältöä ei l<PERSON>yt", "No content found in file.": "Sisältöä ei löytynyt tiedostosta.", "No content to speak": "Ei puhuttavaa sisältöä", "No distance available": "Etäisyyttä ei saatavilla", "No feedbacks found": "Palautteita ei lö<PERSON>", "No file selected": "Tiedostoa ei ole valittu", "No groups with access, add a group to grant access": "<PERSON><PERSON> ryhm<PERSON>, j<PERSON><PERSON> on pääsy, lis<PERSON><PERSON> ryhmä antaaksesi pääsyn", "No HTML, CSS, or JavaScript content found.": "HTML-, CSS- tai <PERSON>Script-sisältöä ei löytynyt.", "No inference engine with management support found": "", "No knowledge found": "Tietoa ei l<PERSON>", "No memories to clear": "Ei muistia tyhjennettäväksi", "No model IDs": "<PERSON><PERSON> mall<PERSON>", "No models found": "Malleja ei lö<PERSON>", "No models selected": "<PERSON>eja ei ole valittu", "No Notes": "<PERSON><PERSON> muist<PERSON>", "No results found": "<PERSON><PERSON> tuloksia", "No search query generated": "Hakukyselyä ei luotu", "No source available": "Lähdettä ei saatavilla", "No users were found.": "Käyttäjiä ei lö<PERSON>.", "No valves to update": "Ei venttiileitä päivitettäväksi", "None": "<PERSON><PERSON> mi<PERSON>n", "Not factually correct": "<PERSON><PERSON> fak<PERSON> o<PERSON>in", "Not helpful": "<PERSON><PERSON>", "Note deleted successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Huomautus: <PERSON><PERSON> v<PERSON>äispistemä<PERSON><PERSON><PERSON><PERSON>, haku palauttaa vain sellaiset asiakirjat, joiden pistemäärä on vähintään vähimmäismäärä.", "Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Notification Sound": "Il<PERSON>it<PERSON><PERSON><PERSON><PERSON>", "Notification Webhook": "Webhook ilmoitus", "Notifications": "Ilmoitukset", "November": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OAuth ID": "OAuth-tunnus", "October": "lokakuu", "Off": "<PERSON><PERSON> p<PERSON>", "Okay, Let's Go!": "<PERSON><PERSON>, menn<PERSON><PERSON>n!", "OLED Dark": "OLED-tumma", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API -asetukset päivitetty", "Ollama Version": "Ollama-versio", "On": "Päällä", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "<PERSON><PERSON> k<PERSON>, numerot ja väliviivat ovat sallittuja", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON>, numerot ja väliviivat ovat sallittuja komentosarjassa.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>ain koko<PERSON>mia voi muokata, luo uusi tietokanta muokataksesi/lisätäks<PERSON> as<PERSON>kir<PERSON>.", "Only markdown files are allowed": "Vain markdown tied<PERSON><PERSON> ovat salli<PERSON>ja", "Only select users and groups with permission can access": "Vain valitut käyttäjät ja ryhm<PERSON>, j<PERSON><PERSON> on käyttöoikeus, pääsevät käyttämään", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hups! Näyttää siltä, että verkko-osoite on virheellinen. Tarkista se ja yritä uudelleen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hups! Tiedost<PERSON> on vielä ladattavana. Odota, ett<PERSON> lataus on valmis.", "Oops! There was an error in the previous response.": "Hups! Edellisessä vastauksessa oli virhe.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hups! Käytät ei-tuettua menetelm<PERSON>ä (vain frontend). Palvele WebUI:ta backendistä.", "Open file": "<PERSON><PERSON>", "Open in full screen": "<PERSON><PERSON> koko n<PERSON>n tilaan", "Open modal to configure connection": "", "Open new chat": "Avaa uusi keskustelu", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI voi käyttää minkä tahansa OpenAPI-palvelimen tarjoamia työkaluja.", "Open WebUI uses faster-whisper internally.": "Open WebUI käyttää faster-<PERSON><PERSON> si<PERSON>.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI käyttää SpeechT5:tä ja CMU Arctic -kaiuttimen upotuksia.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI -versio (v{{OPEN_WEBUI_VERSION}}) on alempi kuin vaadittu versio (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API -asetukset", "OpenAI API Key is required.": "OpenAI API -avain vaaditaan.", "OpenAI API settings updated": "OpenAI API -asetukset päivitetty", "OpenAI URL/Key required.": "OpenAI URL/avain vaaditaan.", "openapi.json URL or Path": "openapi.json verkko-osoite tai polku", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "tai", "Organize your users": "Järjestä k<PERSON>täjäsi", "Other": "<PERSON><PERSON>", "OUTPUT": "TULOSTE", "Output format": "<PERSON><PERSON><PERSON> muoto", "Output Format": "<PERSON><PERSON><PERSON> muoto", "Overview": "Yleiskatsaus", "page": "sivu", "Paginate": "<PERSON><PERSON><PERSON>", "Parameters": "Parametrit", "Password": "<PERSON><PERSON><PERSON>", "Paste Large Text as File": "Liitä suuri teksti tied<PERSON>", "PDF document (.pdf)": "PDF-as<PERSON><PERSON><PERSON><PERSON> (.pdf)", "PDF Extract Images (OCR)": "Poimi kuvat PDF:stä (OCR)", "pending": "odottaa", "Pending": "Odottaa", "Pending User Overlay Content": "Odottavien käyttäjien sisältö", "Pending User Overlay Title": "Odottavien käyttäjien otsikko", "Permission denied when accessing media devices": "Käyttöoikeus evätty media-laitteille", "Permission denied when accessing microphone": "Käyttöoikeus evätty mikrofonille", "Permission denied when accessing microphone: {{error}}": "Käyttöoikeus evätty mikrofonille: {{error}}", "Permissions": "Käyttöoikeudet", "Perplexity API Key": "Perplexity API-avain", "Perplexity Model": "Perplexity malli", "Perplexity Search Context Usage": "Perplexity Search kontekstin k<PERSON>tö", "Personalization": "<PERSON><PERSON><PERSON>", "Picture Description API Config": "Picture Description API konfiguraatio", "Picture Description Local Config": "Picture Description paikallinen konfiguraatio", "Picture Description Mode": "Picture Description tila", "Pin": "Kiinnitä", "Pinned": "<PERSON><PERSON><PERSON><PERSON>", "Pioneer insights": "<PERSON><PERSON>", "Pipeline deleted successfully": "<PERSON><PERSON> poistettu onnist<PERSON>esti", "Pipeline downloaded successfully": "<PERSON><PERSON> ladattu onnist<PERSON>esti", "Pipelines": "Putkistot", "Pipelines Not Detected": "Putkistoja ei havaittu", "Pipelines Valves": "Put<PERSON><PERSON><PERSON><PERSON>", "Plain text (.md)": "<PERSON>elk<PERSON><PERSON> teksti (.md)", "Plain text (.txt)": "Pelkk<PERSON> teksti (.txt)", "Playground": "Leikkipaikka", "Playwright Timeout (ms)": "Playwright <PERSON><PERSON><PERSON><PERSON><PERSON> (ms)", "Playwright WebSocket URL": "Playwright WebSocket verkko-osoite", "Please carefully review the following warnings:": "Tarkista huolellisesti se<PERSON> var<PERSON>:", "Please do not close the settings page while loading the model.": "<PERSON><PERSON><PERSON> sulje as<PERSON><PERSON><PERSON>a mallin latautuessa.", "Please enter a prompt": "<PERSON><PERSON><PERSON><PERSON> kehote", "Please enter a valid path": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> polku", "Please enter a valid URL": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> verkko-osoite", "Please fill in all fields.": "Täytä kaikki kentät.", "Please select a model first.": "Valitse ensin malli.", "Please select a model.": "Valitse malli.", "Please select a reason": "Valitse syy", "Port": "<PERSON><PERSON>", "Positive attitude": "<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "Prefix ID": "Etuliite-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Etuliite-ID:tä käytetään välttämään ristiriidat muiden yhteyksien kanssa lisäämällä etuliite mallitunnuksiin - jätä tyhjäksi, jos haluat ottaa sen pois käytöstä", "Prevent file creation": "", "Preview": "Esikatselu", "Previous 30 days": "Edelliset 30 päivää", "Previous 7 days": "Edelliset 7 päivää", "Previous message": "<PERSON><PERSON><PERSON> viesti", "Private": "<PERSON><PERSON><PERSON><PERSON>", "Profile Image": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Prompt": "<PERSON><PERSON><PERSON>", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "<PERSON><PERSON><PERSON> (es<PERSON>. <PERSON><PERSON> fakta <PERSON> valtakunnasta)", "Prompt Autocompletion": "Kehotteen automaattinen täydennys", "Prompt Content": "Kehotteen sisältö", "Prompt created successfully": "<PERSON><PERSON><PERSON> luotu on<PERSON>", "Prompt suggestions": "Kehotteen ehdotukset", "Prompt updated successfully": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> onnistuneesti", "Prompts": "Ke<PERSON><PERSON><PERSON>", "Prompts Access": "Kehoitteiden käyttöoikeudet", "Prompts Public Sharing": "<PERSON><PERSON><PERSON><PERSON> julkinen jakaminen", "Public": "<PERSON><PERSON><PERSON>", "Pull \"{{searchValue}}\" from Ollama.com": "Lataa \"{{searchValue}}\" Ollama.comista", "Pull a model from Ollama.com": "Lataa malli Ollama.comista", "Query Generation Prompt": "Kyselytulosten luontikehote", "RAG Template": "RAG-kehote", "Rating": "Arviointi", "Re-rank models by topic similarity": "Uudelleenjärjestä mallit aiheyhteyden mukaan", "Read": "<PERSON><PERSON>", "Read Aloud": "<PERSON><PERSON>", "Reason": "", "Reasoning Effort": "", "Record": "<PERSON><PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON><PERSON><PERSON>", "Redirecting you to Open WebUI Community": "Ohjataan sinut OpenWebUI-yhteisöön", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Viittaa itseen \"Käyttäjänä\" (esim. \"Käyttäjä opiskelee espanjaa\")", "References from": "Viitteet lähteistä", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vaikka ei olisi <PERSON>t", "Regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reindex": "Indeksoi <PERSON>", "Reindex Knowledge Base Vectors": "Indeksoi tietämyksen vektorit uudelleen", "Release Notes": "Julkaisutiedot", "Releases": "Julkaisut", "Relevance": "<PERSON><PERSON><PERSON><PERSON>", "Relevance Threshold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Remove": "Poista", "Remove {{MODELID}} from list.": "", "Remove Model": "Poista malli", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON>", "Reorder Models": "Uudelleenjärjest<PERSON> malleja", "Reply in Thread": "Vastauksia ", "Reranking Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moottori", "Reranking Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset": "<PERSON><PERSON><PERSON>", "Reset All Models": "<PERSON><PERSON><PERSON> kaikki mallit", "Reset Upload Directory": "<PERSON><PERSON><PERSON>", "Reset Vector Storage/Knowledge": "Tyhjennä vektoritallennukset/tietämys", "Reset view": "<PERSON><PERSON><PERSON>", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Vastausilmoituksia ei voida otta<PERSON> k<PERSON>öö<PERSON>, koska verkkosivuston käyttöoikeudet on evätty. Myönnä tarvittavat käyttöoikeudet selaimesi asetuksista.", "Response splitting": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>n", "Response Watermark": "Vastauksen vesileima", "Result": "<PERSON><PERSON>", "Retrieval": "<PERSON><PERSON>", "Retrieval Query Generation": "<PERSON><PERSON><PERSON><PERSON><PERSON> luo<PERSON>n", "Rich Text Input for Chat": "Rikasteksti-<PERSON><PERSON><PERSON><PERSON> chattiin", "RK": "RK", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "Ros<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON>", "Running": "K<PERSON>ynnissä", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "<PERSON><PERSON>na ja luo", "Save & Update": "Tall<PERSON>na ja päivitä", "Save As Copy": "<PERSON><PERSON><PERSON> k<PERSON>a", "Save Tag": "<PERSON><PERSON><PERSON> tagi", "Saved": "Tallennettu", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Keskustelulokien tallentaminen suoraan selaimen tallennustilaan ei ole enää tuettu. Lataa ja poista keskustelulokit napsauttamalla alla olevaa painiketta. <PERSON><PERSON><PERSON>, voit helposti tuoda keskustelulokit takaisin backendiin", "Scroll On Branch Change": "Vieritä haaran vaihtoon", "Search": "<PERSON><PERSON>", "Search a model": "Hae mallia", "Search Base": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search Chats": "Hae keskusteluja", "Search Collection": "<PERSON><PERSON> koko<PERSON>a", "Search Filters": "Hakusuodattimet", "search for tags": "hae tageja", "Search Functions": "<PERSON><PERSON> to<PERSON>", "Search Knowledge": "<PERSON>e tietämystä", "Search Models": "<PERSON><PERSON> malleja", "Search options": "Hakuvaihtoehdot", "Search Prompts": "Hae kehotteita", "Search Result Count": "Hakutulosten määrä", "Search the internet": "Hae verkosta", "Search Tools": "Hae työkaluja", "SearchApi API Key": "SearchApi API -avain", "SearchApi Engine": "SearchApi-moottori", "Searched {{count}} sites": "Etsitty {{count}} sivulta", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> tiet<PERSON> \"{{searchQuery}}\"", "Searching the web...": "<PERSON>etaan verkosta...", "Searxng Query URL": "Searxng-k<PERSON><PERSON><PERSON> verk<PERSON>-osoite", "See readme.md for instructions": "<PERSON><PERSON> readme.md-tiedost<PERSON><PERSON>", "See what's new": "<PERSON><PERSON>, mit<PERSON> uutta", "Seed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select a base model": "Valitse <PERSON>li", "Select a engine": "<PERSON><PERSON><PERSON> moottori", "Select a function": "Valitse toiminto", "Select a group": "Valitse ryhmä", "Select a model": "Valitse malli", "Select a pipeline": "<PERSON><PERSON><PERSON> <PERSON>ki", "Select a pipeline url": "Valitse putken verkko-osoite", "Select a tool": "Valitse työkalu", "Select an auth method": "Valitse kirjautumistapa", "Select an Ollama instance": "Valitse Ollama instanssi", "Select Engine": "<PERSON><PERSON><PERSON> moottori", "Select Knowledge": "Valitse tietämys", "Select only one model to call": "Valitse vain yksi malli kutsuttavaksi", "Selected model(s) do not support image inputs": "Valitut mallit eivät tue kuvasöytteitä", "Semantic distance to query": "Semanttinen etä<PERSON><PERSON><PERSON>", "Send": "Lähetä", "Send a Message": "Lähetä viesti", "Send message": "Lähetä viesti", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Lähettää `stream_options: { include_usage: true }` pyynnössä.\nTuetut tarjoajat palauttavat tokenkäyttötiedot vastauksessa, kun se on asetettu.", "September": "syyskuu", "SerpApi API Key": "SerpApi API -avain", "SerpApi Engine": "<PERSON><PERSON><PERSON><PERSON> moottori", "Serper API Key": "Serper API -avain", "Serply API Key": "Serply API -avain", "Serpstack API Key": "Serpstack API -avain", "Server connection verified": "Palvelinyht<PERSON><PERSON> v<PERSON>", "Set as default": "Aseta oletukseksi", "Set CFG Scale": "Aseta CFG-mitta", "Set Default Model": "<PERSON><PERSON>", "Set embedding model": "<PERSON><PERSON>", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON> upotelmamalli (esim. {{model}})", "Set Image Size": "Aseta kuvan koko", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON> u<PERSON>lleenpisteytymismalli (esim. {{model}})", "Set Sampler": "Aseta näytteistäjä", "Set Scheduler": "<PERSON><PERSON> a<PERSON>n", "Set Steps": "Aseta askeleet", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Aseta näytönohjaimelle ladattavien tasojen määrä. Tämän arvon kasvattaminen voi parantaa merkittävästi näytönohjaimelle optimoitujen mallien suorituskykyä, mutta se voi myös lisätä näytönohjaimen virrankulutusta ja resurssien käyttöä.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Aseta työntekijäsäikeiden määrä laskentaa varten. Tämä asetus kontrolloi, kuinka monta säiettä käytetään saapuvien pyyntöjen rinnakkaiseen käsittelyyn. <PERSON><PERSON><PERSON> kasvat<PERSON>n voi parantaa suorituskykyä suurissa samanaikaisissa työkuormissa, mutta voi myös kuluttaa enemmän keskussuorittimen resursseja.", "Set Voice": "<PERSON><PERSON>", "Set whisper model": "Aseta whisper-malli", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "Määrittää kuinka kauas taaksep<PERSON>in malli katsoo toistumisen estämiseksi.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Määrittä satunnainen siemenluku luomista varten. <PERSON><PERSON> si<PERSON>, malli tuottaa saman vastauksen samalle kehotteelle.", "Sets the size of the context window used to generate the next token.": "Määrittää konteksti-ikkunan koon seuraavaksi luotavalle tokenille.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Määrittää käytettävät lopetussekvenssit. Kun tämä kuvio <PERSON>, LLM lopettaa tekstin tuottamisen ja palauttaa. Useita lopetuskuvioita voidaan asettaa määrittämällä useita erillisiä lopetusparametreja mallitiedostoon.", "Settings": "Asetukset", "Settings saved successfully!": "Asetukset tallennettu onnistuneesti!", "Share": "Jaa", "Share Chat": "Jaa keskustelu", "Share to Open WebUI Community": "Jaa OpenWebUI-yhteisöön", "Sharing Permissions": "<PERSON><PERSON><PERSON>", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Näytä", "Show \"What's New\" modal on login": "Näytä \"Mitä uutta\" -modaali kirjautumisen yhteydessä", "Show Admin Details in Account Pending Overlay": "Näytä ylläpitäjän tiedot odottavan tilin päällä", "Show All": "Näytä kaikki", "Show Less": "Näytä vähemmän", "Show Model": "Näytä malli", "Show shortcuts": "Näytä pikanäppäimet", "Show your support!": "<PERSON><PERSON><PERSON> tukesi!", "Showcased creativity": "<PERSON><PERSON><PERSON><PERSON> luovuutta", "Sign in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>n palveluun {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>än palveluun {{WEBUI_NAME}} LDAP:lla", "Sign Out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign up": "Rekisteröidy", "Sign up to {{WEBUI_NAME}}": "Rekisteröidy palveluun {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "Parantaa merkittävästi tarkkuutta käyttämällä LLM:<PERSON><PERSON> taulu<PERSON>, lo<PERSON><PERSON><PERSON><PERSON>, matemat<PERSON><PERSON> ja asettelun havaitsemisen parantamiseen. Lisää viivettä. Oletusarvo on käytössä.", "Signing in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>än palveluun {{WEBUI_NAME}}", "sk-1234": "", "Skip Cache": "<PERSON><PERSON> v<PERSON>", "Skip the cache and re-run the inference. Defaults to False.": "<PERSON><PERSON> välimuisti ja suorita päätelmä uudelleen. Oletusarvo ei käytössä.", "Sougou Search API sID": "Sougou Search API sID", "Sougou Search API SK": "Sougou Search API SK", "Source": "Lä<PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON><PERSON> nopeus", "Speech recognition error: {{error}}": "Puheentunnistusvir<PERSON>: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stop": "Pysäytä", "Stop Generating": "", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stream Chat Response": "Streamaa keskusteluvastaus", "Strip Existing OCR": "Poista olemassa oleva OCR", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Poista olemassa oleva OCR-teksti PDF-tiedostosta ja suorita OCR uudelleen. Ohite<PERSON>an, jos pakota OCR -asetus on käytössä. Oletusarvo ei käytössä.", "STT Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "STT Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "Stylized PDF Export": "Muotoiltun PDF-vienti", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (esim. <PERSON><PERSON> v<PERSON>)", "Success": "<PERSON><PERSON><PERSON>", "Successfully updated.": "<PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti.", "Suggested": "Ehdotukset", "Support": "<PERSON><PERSON>", "Support this plugin:": "<PERSON>e tätä lisäosaa:", "Supported MIME Types": "", "Sync directory": "Synkron<PERSON><PERSON> hake<PERSON>o", "System": "Järjestelmä", "System Instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "System Prompt": "Järjestelmäkehote", "Tags": "Tagit", "Tags Generation": "<PERSON><PERSON> luonti", "Tags Generation Prompt": "<PERSON><PERSON> luo<PERSON>", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "<PERSON><PERSON><PERSON> mallille", "Tap to interrupt": "Napauta keskeyttääksesi", "Task Model": "Työmalli", "Tasks": "Tehtävät", "Tavily API Key": "Tavily API -avain", "Tavily Extract Depth": "<PERSON>ly poiminta syvyys", "Tell us more:": "<PERSON><PERSON>:", "Temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Temporary Chat": "Väliaikainen keskustelu", "Text Splitter": "<PERSON><PERSON><PERSON>", "Text-to-Speech": "", "Text-to-Speech Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Thanks for your feedback!": "Ki<PERSON>s p<PERSON>utt<PERSON>i!", "The Application Account DN you bind with for search": "<PERSON><PERSON>a varten sidottu sovelluksen käyttäjätilin DN", "The base to search for users": "K<PERSON>yttäji<PERSON> haun perusta", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>, kuinka monta tekstipyyntöä käsitellään kerralla. Suurempi eräkoko voi parantaa mallin suorituskykyä ja nopeutta, mutta se vaatii myös enemmän muistia.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Tämän lisäosan takana olevat kehittäjät ovat intohimoisia vapaaehtoisyhteisöstä. <PERSON><PERSON> koet tämän lisäosan hyödylliseksi, harkitse sen kehittämisen tukemista.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Arviointitulosluettelo perustuu Elo-luokitusjärjestelmään ja päivittyy reaaliajassa.", "The format to return a response in. Format can be json or a JSON schema.": "<PERSON><PERSON>, jolla vastaus pala<PERSON>. <PERSON><PERSON> voi olla json- tai JSON-skeema.", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "Syöteäänen kieli. Syöttökielen antaminen ISO-639-1-muo<PERSON>sa (esim. en) parantaa tarkkuutta ja viivettä. Jätä ty<PERSON>, jos haluat kielen automaattisen tunnistuksen.", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP-määrite, joka yhdistä<PERSON> käyttäjien kirjautumiseen käyttämään sähköpostiin.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-määrite, joka vastaa käyttäjien kirjautumiskäyttäjänimeä.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "<PERSON><PERSON><PERSON><PERSON><PERSON> on tällä hetkellä beta-v<PERSON><PERSON><PERSON>, ja voimme säätää pisteytyksen laskentaa hienostaessamme algoritmia.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Enimmäistiedostokoko megata<PERSON>. <PERSON><PERSON> tied<PERSON>on koko ylittää tämän rajan, tied<PERSON><PERSON> ei ladata.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "<PERSON><PERSON><PERSON> sallittu tiedostojen määrä käytettäväksi kerralla chatissa. <PERSON><PERSON> tiedostojen määrä ylittää tämän rajan, niitä ei ladata.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> olla 'json', 'markdown' tai 'html'. Oletusar<PERSON> on 'markdown'.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Pisteytyksen tulee olla arvo välillä 0,0 (0 %) ja 1,0 (100 %).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Mallin lämpötila. Lisäämällä lämpötilaa mallin vastaukset ovat luovempia.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "A<PERSON><PERSON><PERSON>...", "This action cannot be undone. Do you wish to continue?": "Tätä toimintoa ei voi peruuttaa. Haluatko jatkaa?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "<PERSON>ä<PERSON><PERSON> kanava on luotiin {{createdAt}}. Tä<PERSON><PERSON> on {{channelName}} kanavan alku.", "This chat won't appear in history and your messages will not be saved.": "Tämä keskustelu ei näy historiassa, eikä viestej<PERSON>si tallenneta.", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Tämä varmistaa, että arvokkaat keskustelusi tallennetaan turvallisesti backend-tietokantaasi. Kiitos!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Tämä on kokeellinen ominaisuus, se ei välttämättä toimi odotetulla tavalla ja se voi muuttua milloin tahansa.", "This model is not publicly available. Please select another model.": "Tämä malli ei ole julkisesti saatavilla. Valitse toinen malli.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "<PERSON><PERSON><PERSON><PERSON> asetus mä<PERSON>tää kuinka kauan malli pysyy ladattuna muistissa pyynnön jälk<PERSON> (oletusarvo: 5m)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Tämä as<PERSON> m<PERSON>, kuinka monta tokenia säilytetään kontekstia päivitettäessä. <PERSON><PERSON><PERSON> on asetettu esimerkiksi 2, keskustelukontekstin kaksi viimeistä tokenia säilytetään. Kontekstin säilyttäminen voi auttaa ylläpitämään keskustelun jatkuvuutta, mutta se voi heikentää kykyä vastata uusiin aiheisiin.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "<PERSON>ämä vaihtoehto as<PERSON>a mallin vastauksessaan luomien tokenien enimmäismäärän. Tämän rajan nostaminen antaa mallille mahdollisuuden tarjota pidempiä vastauksia, mutta se voi myös lisätä hyödyttömän tai epäolennaisen sisällön luomisen todennäköisyyttä.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Tämä vaihtoehto poistaa kaikki koko<PERSON>man n<PERSON> tiedostot ja korvaa ne uusilla ladatuilla tiedostoilla.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuotti \"{{model}}\"", "This will delete": "<PERSON><PERSON><PERSON>ä poistaa", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON><PERSON> poistaa <strong>{{NAME}}</strong> ja <strong>kaikki sen sis<PERSON><PERSON><PERSON>t</strong>.", "This will delete all models including custom models": "Tämä poistaa kaikki mallit mukaan lukien mukautetut mallit", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON><PERSON><PERSON> poistaa kaikki mallit, mukaan lukien mukautetut mallit, eikä sitä voi peruuttaa.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON>ä<PERSON>ä nollaa tietokannan ja synkronoi kaikki tied<PERSON>ot. Haluatko jatkaa?", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "Thought for {{DURATION}}": "<PERSON><PERSON><PERSON><PERSON> {{DURATION}}", "Thought for {{DURATION}} seconds": "Ajatteli {{DURATION}} sekunttia", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "<PERSON>ika palvelimen verkko-osoite vaaditaan.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Vinkki: Päivitä useita muuttujapaikkoja peräkkäin painamalla tabulaattoria keskustelusyötteessä jokaisen korvauksen jälkeen.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (es<PERSON>. <PERSON><PERSON> fakta)", "Title Auto-Generation": "Otsikon automaattinen luonti", "Title cannot be an empty string.": "Otsikko ei voi olla tyhjä merkkijono.", "Title Generation": "Otsikon luonti", "Title Generation Prompt": "Otsikon luontikehote", "TLS": "TLS", "To access the available model names for downloading,": "Päästäksesi käsiksi ladattavissa oleviin mallinimiin,", "To access the GGUF models available for downloading,": "Päästäksesi käsiksi ladattavissa oleviin GGUF-malleihin,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Päästäksesi k<PERSON>tämään WebUI:ta, ota yhteyttä ylläpitäjään. Ylläpitäjät voivat hallita käyttäjien tiloja Ylläpitopaneelista.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Liittääks<PERSON> tie<PERSON> tä<PERSON>, lis<PERSON><PERSON> ne ensin \"Tiet<PERSON><PERSON><PERSON>\"-työtilaan.", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON> ha<PERSON>at lisätietoja käytettävissä olevista päätepisteistä, tutustu dokumentaatioomme.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Yksityisyydensuojasi vuoksi palautteestasi jaetaan vain arvostelut, mallit<PERSON><PERSON><PERSON><PERSON>, tagit ja metadata - keskustelulokisi pysyvät yksityisinä eikä niitä sisällytetä.", "To select actions here, add them to the \"Functions\" workspace first.": "Valitaks<PERSON> toimintoja tässä, lisä<PERSON> ne ensin \"To<PERSON><PERSON>ot\"-työtilaan.", "To select filters here, add them to the \"Functions\" workspace first.": "Valitaksesi suodattimia tässä, lisä<PERSON> ne ensin \"<PERSON><PERSON><PERSON>ot\"-työtilaan.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Valitaksesi työkalusettejä tässä, lisää ne ensin \"Työkalut\"-työtilaan.", "Toast notifications for new updates": "Ilmoituspopuppien näyttäminen uusista päivityksistä", "Today": "Tänää<PERSON>", "Toggle search": "<PERSON><PERSON><PERSON> haku", "Toggle settings": "<PERSON><PERSON><PERSON>", "Toggle sidebar": "<PERSON><PERSON><PERSON>", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON><PERSON>", "Tool created successfully": "Työkalu luotu onnistuneesti", "Tool deleted successfully": "Työkalu poistettu onnistuneesti", "Tool Description": "Työkalun kuvaus", "Tool ID": "<PERSON><PERSON><PERSON><PERSON> tunnus", "Tool imported successfully": "Työkalu tuotu onnistuneesti", "Tool Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Tool Servers": "Työ<PERSON><PERSON> palvelin", "Tool updated successfully": "Työkalu päivitetty onnistuneesti", "Tools": "Työkalut", "Tools Access": "Työkalujen k<PERSON>öoikeudet", "Tools are a function calling system with arbitrary code execution": "Työkalut ovat toimintokutsuihin perustuva jär<PERSON><PERSON>lmä, joka sallii mi<PERSON> koodin su<PERSON>tta<PERSON>n", "Tools Function Calling Prompt": "Työkalujen k<PERSON>ukehote", "Tools have a function calling system that allows arbitrary code execution.": "Työkalut sallivat mieli<PERSON>sen koodin suorittamisen toimintokutsuilla.", "Tools Public Sharing": "Työkalujen julkinen jakaminen", "Top K": "Top K", "Top K Reranker": "Top K uudelleen sijoittaja", "Transformers": "Muunnokset", "Trouble accessing Ollama?": "Ongelmia Ollama-yhteydessä?", "Trust Proxy Environment": "Luota välityspalvelimen ympäristöön", "TTS Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TTS Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>setukset", "TTS Voice": "Puhesynteesiääni", "Type": "Tyyppi", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON><PERSON><PERSON> Face -resolve-latausosoite", "Uh-oh! There was an issue with the response.": "Voi ei! Vastauksessa ilmeni ongel<PERSON>.", "UI": "Käyttöliittymä", "Unarchive All": "<PERSON>ura kaikkien a<PERSON>istointi", "Unarchive All Archived Chats": "Pura kaikkien arkistoitujen keskustelujen arkistointi", "Unarchive Chat": "<PERSON>ura keskustelun arkistointi", "Unloads {{FROM_NOW}}": "<PERSON><PERSON><PERSON><PERSON> {{FROM_NOW}}", "Unlock mysteries": "Selvitä arvoituksia", "Unpin": "Irrota kiinnitys", "Unravel secrets": "<PERSON><PERSON>", "Untagged": "<PERSON><PERSON> tageja", "Untitled": "Nimetön", "Update": "Päivitä", "Update and Copy Link": "Päivitä ja kopioi linkki", "Update for the latest features and improvements.": "Päivitä uusim<PERSON>in ominaisuuksiin ja parannuksiin.", "Update password": "Päivitä sa<PERSON>", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated At": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Päivitä lisenssi saadaksesi parempia ominaisuuk<PERSON>, mukaan lukien mukautetun teeman ja brä<PERSON><PERSON><PERSON><PERSON> sekä yksilöllistä tukea.", "Upload": "Lataa", "Upload a GGUF model": "Lataa GGUF-malli", "Upload Audio": "Lataa äänitiedosto", "Upload directory": "<PERSON><PERSON><PERSON> hake<PERSON>o", "Upload files": "<PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON> putki", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON> ed<PERSON><PERSON>", "URL": "URL", "URL Mode": "URL-tila", "Usage": "Käyttö", "Use '#' in the prompt input to load and include your knowledge.": "Käytä '#' -merk<PERSON>ä kehotekenttään ladataksesi ja sisällyttääksesi tietämystäsi.", "Use Gravatar": "Käytä Gravataria", "Use groups to group your users and assign permissions.": "Käytä ryhmiä jäsentääksesi käyttäjiä ja antaaksesi käyttöoikeuk<PERSON>.", "Use Initials": "Käytä alkukirjaimia", "Use LLM": "Käytä LLM:ää", "Use no proxy to fetch page contents.": "Älä käytä välityspalvelinta sivun tietoja haettaessa.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Käytä http_proxy- ja https_proxy-ympäristömuuttujien määrittämää välityspalvelinta sivun sisällön hakemiseen.", "user": "käyttäjä", "User": "Käyttäjä", "User location successfully retrieved.": "Käyttäjän sijainti haettu onnistuneesti.", "User Webhooks": "Käyttäjän Webhook:it", "Username": "K<PERSON>yttäjät<PERSON>nus", "Users": "Käyttäjät", "Using the default arena model with all models. Click the plus button to add custom models.": "Käytetään o<PERSON>-mallia kaikkien mallien kanssa. Napsauta plus-painiketta lisätäks<PERSON> mukautettuja malleja.", "Utilize": "Hyödynnä", "Valid time units:": "Kelvolliset aikayksiköt:", "Valves": "Venttiilit", "Valves updated": "Ventti<PERSON>t päiv<PERSON>tty", "Valves updated successfully": "Venttiilit päivitetty onnistuneesti", "variable": "mu<PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "muuttuja korvataan leikepöydän sisällöllä.", "Verify Connection": "Tarkista yhteys", "Verify SSL Certificate": "Tarkista SSL-varmenne", "Version": "Versio", "Version {{selectedVersion}} of {{totalVersions}}": "Versio {{selectedVersion}} / {{totalVersions}}", "View Replies": "Näytä vastaukset", "View Result from **{{NAME}}**": "Näytä **{{NAME}}** tulokset", "Visibility": "Näkyvyys", "Vision": "Visio", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "Äänitulolaitteen käyttö", "Voice mode": "<PERSON><PERSON><PERSON><PERSON>", "Warning": "Varo<PERSON><PERSON>", "Warning:": "Varoitus:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Varoitus: <PERSON><PERSON><PERSON><PERSON><PERSON> kä<PERSON>öönotto sallii käyttäjien ladata mielivaltaista koodia palvelimelle.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varoitus: <PERSON><PERSON> p<PERSON> tai vaihdat upotus<PERSON>, sinun on tuotava kaikki asia<PERSON><PERSON><PERSON><PERSON> u<PERSON>.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Varoitus: <PERSON><PERSON><PERSON> voi mahdollistaa mieli<PERSON>seen koodin suoritta<PERSON>en, mikä voi aiheuttaa tietoturvariskejä - käytä äärimmäisen varoen.", "Web": "Web", "Web API": "Web-API", "Web Loader Engine": "Verkkolat<PERSON><PERSON> moottori", "Web Search": "Verkkohaku", "Web Search Engine": "Hakukon<PERSON>t", "Web Search in Chat": "Verkkohaku keskustelussa", "Web Search Query Generation": "Verkko<PERSON><PERSON><PERSON><PERSON><PERSON> luo<PERSON>", "Webhook URL": "Webhook verkko-osoite", "WebUI Settings": "WebUI-asetukset", "WebUI URL": "WebUI-osoite", "WebUI will make requests to \"{{url}}\"": "WebUI lähettää pyyntöjä osoitteeseen \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI lähettää pyyntöjä osoitteeseen \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI lähettää pyyntöjä osoitteeseen \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "BM25-ha<PERSON>", "What are you trying to achieve?": "Mitä yrität saavuttaa?", "What are you working on?": "Mitä olet t<PERSON>ntelemässä?", "What's New in": "<PERSON><PERSON><PERSON>", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON> k<PERSON><PERSON>, malli vastaa jokaiseen chatviestiin reaaliajas<PERSON>, tuottaen vastauksen heti kun käyttäjä lähettää viestin. Tämä tila on hyödyllinen reaaliaikaisissa chat-sovelluksissa, mutta voi vaikuttaa suorituskykyyn hitaammilla laitteistoilla.", "wherever you are": "miss<PERSON> ta<PERSON><PERSON>", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Sivutetaanko t<PERSON>. Jokainen sivu erotetaan toisistaan ​​vaakasuoralla viivalla ja sivunumerolla. Oletusarvo ei käytössä.", "Whisper (Local)": "Whisper (paikallinen)", "Why?": "<PERSON><PERSON><PERSON>?", "Widescreen Mode": "Laajakuvatila", "Won": "<PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "<PERSON><PERSON><PERSON><PERSON>", "Workspace Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Write": "<PERSON><PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON><PERSON> keh<PERSON> ehdo<PERSON> (esim. <PERSON><PERSON> olet?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Kirjoita 50 sanan y<PERSON>, joka tii<PERSON> [aihe tai ava<PERSON>].", "Write something...": "<PERSON><PERSON><PERSON><PERSON> jotain...", "Yacy Instance URL": "<PERSON><PERSON> instanssin verkko-osoite", "Yacy Password": "<PERSON><PERSON>", "Yacy Username": "<PERSON><PERSON>", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "Sin<PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "Käytät tällä hetkellä kokeiluversiota. Ota yhteyttä tukeen lisenssin päivittämiseksi.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON>oit keskustella enintään {{maxCount}} tiedoston kanssa ker<PERSON>.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Voit personoida vuorovaikutustasi LLM-ohjelmien kanssa lisäämällä muistoja 'Hallitse'-pain<PERSON><PERSON><PERSON> kautta, jolloin ne ovat hyödyllisempiä ja räätälöityjä sinua varten.", "You cannot upload an empty file.": "Et voi ladata tyhjää tiedostoa.", "You do not have permission to upload files.": "<PERSON><PERSON>a ei ole lupaa ladata tiedost<PERSON>.", "You have no archived conversations.": "Sinulla ei ole arkistoituja keskusteluja.", "You have shared this chat": "<PERSON><PERSON> j<PERSON> tämän keskustelun", "You're a helpful assistant.": "<PERSON><PERSON> a<PERSON> a<PERSON>.", "You're now logged in.": "<PERSON>t nyt kirjautunut sis<PERSON>än.", "Your account status is currently pending activation.": "Tilisi tila on tällä hetkellä odottaa aktivointia.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> pan<PERSON> menee suoraan lisäosan kehittäjälle; Open WebUI ei pidätä prosenttiosuutta. Valittu rahoitusalusta voi kuitenkin periä omia maksujaan.", "Youtube": "YouTube", "Youtube Language": "Youtube kieli", "Youtube Proxy URL": "Youtube-välityspalvelimen verkko-osoite"}