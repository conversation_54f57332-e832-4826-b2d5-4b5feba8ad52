{"-1 for no limit, or a positive integer for a specific limit": "-1 para ilimitado, o un número entero positivo para un límite específico.", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' para evitar expiración.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(p.ej. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(p.ej. `sh webui.sh --api`)", "(latest)": "(último)", "(leave blank for to use commercial endpoint)": "(dejar vacío para endpoints commerciales", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} herramientas disponibles", "{{COUNT}} hidden lines": "{{COUNT}} líneas ocultas", "{{COUNT}} Replies": "{{COUNT}} Respuestas", "{{user}}'s Chats": "Chats de {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Ser<PERSON>or <PERSON>", "*Prompt node ID(s) are required for image generation": "Los ID de nodo son requeridos para la generación de imágenes", "A new version (v{{LATEST_VERSION}}) is now available.": "Nueva versión (v{{LATEST_VERSION}}) disponible.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "El modelo de tareas realiza tareas como la generación de títulos para chats y consultas de búsqueda web", "a user": "un usuario", "About": "Acerca de", "Accept autocomplete generation / Jump to prompt variable": "Aceptar generación de autocompletado / Saltar a prompt variable", "Access": "Acceso", "Access Control": "Control de Acceso", "Accessible to all users": "Accesible para todos los usuarios", "Account": "C<PERSON><PERSON>", "Account Activation Pending": "Activación de cuenta Pendiente", "Accurate information": "Información precisa", "Actions": "Acciones", "Activate": "Activar", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Activar este comando escribiendo \"/{{COMMAND}}\" en el chat", "Active Users": "Usuarios activos", "Add": "<PERSON><PERSON><PERSON>", "Add a model ID": "Añadir un ID de modelo", "Add a short description about what this model does": "Añadir una breve descripción sobre lo que hace este modelo", "Add a tag": "Añadir una etiqueta", "Add Arena Model": "Añadir modelo a la Arena", "Add Connection": "Añadir Conexión", "Add Content": "<PERSON><PERSON><PERSON>", "Add content here": "<PERSON><PERSON>dir contenido aquí", "Add Custom Parameter": "<PERSON><PERSON><PERSON> pará<PERSON>ro personalizado", "Add custom prompt": "<PERSON><PERSON>dir un prompt personalizado", "Add Files": "Añadir Archivos", "Add Group": "<PERSON><PERSON><PERSON>", "Add Memory": "<PERSON><PERSON><PERSON>", "Add Model": "<PERSON><PERSON><PERSON>", "Add Reaction": "<PERSON><PERSON><PERSON>", "Add Tag": "Añadir etiqueta", "Add Tags": "Añadir etiquetas", "Add text content": "Añade contenido de texto", "Add User": "<PERSON><PERSON><PERSON>", "Add User Group": "Añadir grupo de usuarios", "Adjusting these settings will apply changes universally to all users.": "El ajuste de estas opciones se aplicará globalmente a todos los usuarios.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Administración", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Los administradores tienen acceso a todas las herramientas en todo momento; los usuarios necesitan que los modelos tengan asignadas las herramientas en el area de trabajo.", "Advanced Parameters": "<PERSON>rá<PERSON><PERSON>", "Advanced Params": "<PERSON>rá<PERSON><PERSON>", "All": "Todos", "All Documents": "Todos los Documentos", "All models deleted successfully": "Todos los modelos borrados correctamnete", "Allow Call": "<PERSON><PERSON><PERSON>", "Allow Chat Controls": "Permitir <PERSON> del Chat", "Allow Chat Delete": "<PERSON><PERSON><PERSON>", "Allow Chat Deletion": "<PERSON><PERSON><PERSON>", "Allow Chat Edit": "<PERSON><PERSON><PERSON><PERSON>", "Allow Chat Export": "<PERSON>mit<PERSON>", "Allow Chat Share": "Permit<PERSON>", "Allow Chat System Prompt": "", "Allow File Upload": "Permitir Subida de Archivos", "Allow Multiple Models in Chat": "<PERSON><PERSON><PERSON><PERSON>", "Allow non-local voices": "Permitir voces no locales", "Allow Speech to Text": "Permitir <PERSON> a Texto", "Allow Temporary Chat": "<PERSON><PERSON><PERSON>", "Allow Text to Speech": "<PERSON><PERSON><PERSON>", "Allow User Location": "Permitir Ubicación de Usuario", "Allow Voice Interruption in Call": "Permitir Interrupción de Voz en Llamada", "Allowed Endpoints": "Endpoints Permitidos", "Allowed File Extensions": "Extensiones de Archivo Permitidas", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Extensiones de archivos permitidas para subir. Si son varias separalas con comas. Dejar vacío para permitir subir archivos con cualquier extensión.", "Already have an account?": "¿Ya tienes una cuenta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternativa a top_p, como objetivo garantizar un equilibrio entre calidad y variedad. El parámetro p representa la mínima probabilidad para que un token sea considerado, relativo a la probabilidad del token más probable. Por ejemplo, con p=0.05 y la probabilidad del token más probable de 0.9, los resultados (logits) con un valor inferior a 0.045 son descartados.", "Always": "Siempre", "Always Collapse Code Blocks": "Plegar Siempre los Bloques de Código", "Always Expand Details": "Expandir Siempre Detalles", "Always Play Notification Sound": "Reproducir Siempre Sonido de Notificación", "Amazing": "Emocionante", "an assistant": "un asistente", "Analyzed": "<PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "Analizando..", "and": "y", "and {{COUNT}} more": "y {{COUNT}} más", "and create a new shared link.": "y crear un nuevo enlace compartido.", "Android": "Android", "API": "API", "API Base URL": "URL Base API", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "Detalles API para usar modelo de visión-lenguaje en las descripciones de las imágenes. Este parámetro es muamente excluyente con \"picture_description_local\" ", "API Key": "Clave API ", "API Key created.": "Clave API creada.", "API Key Endpoint Restrictions": "Clave API para Endpoints Restringidos", "API keys": "Claves API", "API Version": "Version API", "Application DN": "Aplicacion DN", "Application DN Password": "Contraseña Aplicacion DN", "applies to all users with the \"user\" role": "se aplica a todos los usuarios con el rol \"user\" ", "April": "Abril", "Archive": "Archivar", "Archive All Chats": "Archivar Todos los Chats", "Archived Chats": "Chats archivados", "archived-chat-export": "exportar chats archivados", "Are you sure you want to clear all memories? This action cannot be undone.": "¿Segur@ de que quieres borrar todas las memorias? (¡esta acción NO se puede deshacer!)", "Are you sure you want to delete this channel?": "¿Segur@ de que quieres eliminar este canal?", "Are you sure you want to delete this message?": "¿Segur@ de que quieres eliminar este mensaje? ", "Are you sure you want to unarchive all archived chats?": "¿Segur@ de que quieres desarchivar todos los chats archivados?", "Are you sure?": "¿Estás segur@?", "Arena Models": "Arena de Modelos", "Artifacts": "Artefactos", "Ask": "Preguntar", "Ask a question": "Haz una pregunta", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file from knowledge": "Adjuntar archivo desde conocimiento", "Attention to detail": "Atención al detalle", "Attribute for Mail": "Atributo para Correo", "Attribute for Username": "Atributo para Nombre de Usuario", "Audio": "Audio", "August": "Agosto", "Auth": "Autorización", "Authenticate": "Autentificar", "Authentication": "Autenticación", "Auto": "Auto", "Auto-Copy Response to Clipboard": "AutoCopiado de respuesta al Portapapeles", "Auto-playback response": "Reproducir Respuesta automáticamente", "Autocomplete Generation": "Generación de Autocompletado", "Autocomplete Generation Input Max Length": "Max<PERSON> Longitud de Entrada en Generación de Autocompletado", "Automatic1111": "AUTOMATIC1111", "AUTOMATIC1111 Api Auth String": "Auth API para AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL Base de AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "la URL Base de AUTOMATIC1111 es necesaria.", "Available list": "Lista disponible", "Available Tools": "Herramientas Disponibles", "available!": "¡disponible!", "Awful": "Horrible", "Azure AI Speech": "Voz Azure AI", "Azure Region": "Región de Azure", "Back": "Volver", "Bad Response": "Mala Respuesta", "Banners": "Banners", "Base Model (From)": "Modelo Base (desde)", "before": "antes", "Being lazy": "<PERSON> per<PERSON>oso", "Beta": "Beta", "Bing Search V7 Endpoint": "Endpoint de Bing Search V7", "Bing Search V7 Subscription Key": "Clave de Suscripción de Bing Search V7", "Bocha Search API Key": "Clave API de Bocha Search", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Impulsando o penalizando tokens específicos para respuestas restringidas. Los valores de sesgo se limitarán entre -100 y 100 (inclusive). (Por defecto: ninguno)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Clave API de Brave Search", "By {{name}}": "Por {{name}}", "Bypass Embedding and Retrieval": "Desactivar Incrustración y Recuperación", "Bypass Web Loader": "Desactivar Cargar de Web", "Calendar": "Calendario", "Call": "Llamada", "Call feature is not supported when using Web STT engine": "La funcionalidad de Llamada no está soportada cuando se usa el motor Web STT", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "Captura", "Capture Audio": "Capturar Audio", "Certificate Path": "Ruta a Certificado", "Change Password": "Cambiar <PERSON>", "Channel Name": "Nombre del Canal", "Channels": "Canal", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Límite de caracteres de entrada de la generación de autocompletado", "Chart new frontiers": "<PERSON><PERSON><PERSON> nuevas fronteras", "Chat": "Cha<PERSON>", "Chat Background Image": "Imágen de Fondo del Chat", "Chat Bubble UI": "Interface de Chat tipo Burbuja", "Chat Controls": "Controles de chat", "Chat direction": "Dirección de Chat", "Chat Overview": "Vista General del Chat", "Chat Permissions": "Permisos del Chat", "Chat Tags Auto-Generation": "AutoGeneración de Etiquetas de Chat", "Chats": "Chats", "Check Again": "Verifica de nuevo", "Check for updates": "Buscar actualizaciones", "Checking for updates...": "Buscando actualizaciones...", "Choose a model before saving...": "Escoge un modelo antes de guardar...", "Chunk Overlap": "Superposición de Fragmentos", "Chunk Size": "Tamaño de los Fragmentos", "Ciphers": "Cifrado", "Citation": "Cita", "Citations": "Citas", "Clear memory": "Liberar memoria", "Clear Memory": "Liberar Memoria", "click here": "<PERSON><PERSON><PERSON> a<PERSON>", "Click here for filter guides.": "Pulsar aquí para guías de filtros", "Click here for help.": "Pulsar aquí para Ayuda.", "Click here to": "<PERSON><PERSON>sa aquí para", "Click here to download user import template file.": "Pulsa aquí para descargar la plantilla para importar usuarios.", "Click here to learn more about faster-whisper and see the available models.": "Pulsa aquí para saber más sobre faster-whisper y ver los modelos disponibles.", "Click here to see available models.": "Pulsa aquí para ver modelos disponibles.", "Click here to select": "Pulsa aquí para seleccionar", "Click here to select a csv file.": "Pulsa aquí para seleccionar un archivo de Valores Separados por Comas (.csv)", "Click here to select a py file.": "Pulsa aquí para seleccionar un archivo Python (.py)", "Click here to upload a workflow.json file.": "Pulsa aquí para subir un archivo workflow.json", "click here.": "<PERSON><PERSON><PERSON> aqu<PERSON>.", "Click on the user role button to change a user's role.": "Pulsa en el botón rol de usuario para cambiar su rol.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permisos de escritura del portapapeles denegado. Por favor, comprueba la configuración de tu navegador para otorgar el permiso necesario.", "Clone": "Clonar", "Clone Chat": "<PERSON><PERSON><PERSON>", "Clone of {{TITLE}}": "Clon de {{TITLE}}", "Close": "<PERSON><PERSON><PERSON>", "Close modal": "Cerrar modal", "Close settings modal": "Cerrar modal  configuraciones", "Code execution": "Ejecución de Código", "Code Execution": "Ejecución de Código", "Code Execution Engine": "Motor de Ejecución de Código", "Code Execution Timeout": "Tiempo límite de espera para Ejecución de Código", "Code formatted successfully": "El codigo se ha formateado correctamente.", "Code Interpreter": "Interprete de Código", "Code Interpreter Engine": "Motor del Interprete de Código", "Code Interpreter Prompt Template": "Plantilla del Prompt del Interprete de Código", "Collapse": "Plegar", "Collection": "Colección", "Color": "Color", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Clave API de ComfyUI", "ComfyUI Base URL": "URL Base de ComfyUI", "ComfyUI Base URL is required.": "La URL Base de ComfyUI es necesaria.", "ComfyUI Workflow": "Flujo de Trabajo de ComfyUI", "ComfyUI Workflow Nodes": "Nodos del Flujo de Trabajo de ComfyUI", "Command": "Comand<PERSON>", "Completions": "Cumplimientos", "Concurrent Requests": "Número de Solicitudes Concurrentes", "Configure": "Configurar", "Confirm": "Confirmar", "Confirm Password": "<PERSON><PERSON><PERSON>", "Confirm your action": "Confirma tu acción", "Confirm your new password": "Confirma tu nueva contraseña", "Connect to your own OpenAI compatible API endpoints.": "Conectar a tus propios endpoints compatibles API OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "Conectar a tus propios endpoints externos de herramientas compatibles API OpenAI.", "Connection failed": "Conexión fallida", "Connection successful": "Conexión realizada", "Connection Type": "Tipo de Conexión", "Connections": "Conexiones", "Connections saved successfully": "Conexiones grabadas correctamente", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Limita el esfuerzo de razonamiento para los modelos de razonamiento. Solo aplicable a modelos de razonamiento de proveedores específicos que soportan el esfuerzo de razonamiento.", "Contact Admin for WebUI Access": "Contacta con Admin para obtener acceso a WebUI", "Content": "Contenido", "Content Extraction Engine": "Motor para la Extracción de Contenido", "Continue Response": "<PERSON><PERSON><PERSON><PERSON>", "Continue with {{provider}}": "Continuar con {{provider}}", "Continue with Email": "Con<PERSON><PERSON><PERSON> con Email", "Continue with LDAP": "Continuar con LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlar como se divide el texto del mensaje para las solicitudes de locución (TTS). 'Punctuation' divide oraciones, 'paragraphs' divide párrafos y 'none' mantiene el mensaje como una sola cadena.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Controla la repetición de secuencias de tokens en el texto generado. Un valor más alto (p.ej., 1.5) penalizá más las repeticiones, mientras que un valor más bajo (p.ej., 1.1) sería más permisivo. En 1, el control está desactivado.", "Controls": "Controles", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Controles del equilibrio entre coherencia y diversidad de la salida. Un valor más bajo produce un texto más centrado y coherente.", "Copied": "Copiado", "Copied link to clipboard": "Enlace copiado a portapapeles", "Copied shared chat URL to clipboard!": "¡Copiada al portapapeles la URL del chat compartido!", "Copied to clipboard": "Copiado al portapapeles", "Copy": "Copiar", "Copy Formatted Text": "Copiar Texto Formateado", "Copy last code block": "Copia el último bloque de código", "Copy last response": "Copia la última respuesta", "Copy Link": "<PERSON><PERSON><PERSON> enlace", "Copy to clipboard": "Copia a portapapeles", "Copying to clipboard was successful!": "¡La copia al portapapeles se ha realizado correctamente!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "El protocolo CORS debe estar configurado correctamente por el proveedor para permitir solicitudes desde Open WebUI.", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "Crear Base de Conocimiento", "Create a model": "<PERSON><PERSON><PERSON>", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "<PERSON><PERSON><PERSON> Administrativa", "Create Channel": "Crear Canal", "Create Group": "Crear Grupo", "Create Knowledge": "<PERSON><PERSON><PERSON>", "Create new key": "<PERSON><PERSON><PERSON> Clave", "Create new secret key": "<PERSON><PERSON>r Nueva Clave Secreta", "Create Note": "<PERSON><PERSON><PERSON>", "Create your first note by clicking on the plus button below.": "Crea tu primera nota pulsando el botón + de abajo", "Created at": "Creado en", "Created At": "Creado En", "Created by": "<PERSON><PERSON>o por", "CSV Import": "Importar CSV", "Ctrl+Enter to Send": "'Ctrl+Enter' para Enviar", "Current Model": "Modelo Actual", "Current Password": "Contraseña Actual", "Custom": "Personalizado", "Custom Parameter Name": "Nombre del Parámetro Personalizado", "Custom Parameter Value": "Valor del Parámetro Personalizado", "Danger Zone": "Zona Peligrosa", "Dark": "Oscuro", "Database": "Base de datos", "Datalab Marker API": "API de Datalab Marker", "Datalab Marker API Key required.": "Clave API de Datalab Marker Requerida", "December": "Diciembre", "Default": "Predeterminado", "Default (Open AI)": "Predetermina<PERSON> (Open AI)", "Default (SentenceTransformers)": "Predeterminado (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "<PERSON><PERSON>determinado", "Default model updated": "El modelo Predeterminado ha sido actualizado", "Default Models": "Modelos Predeterminados", "Default permissions": "Permisos <PERSON>determina<PERSON>", "Default permissions updated successfully": "Permisos predeterminados actualizados correctamente", "Default Prompt Suggestions": "Sugerencias Predeterminadas de Prompt", "Default to 389 or 636 if TLS is enabled": "Predeterminado a 389, o 636 si TLS está habilitado", "Default to ALL": "Predeterminado a TODOS", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Por defecto está predeterminada una segmentación de la recuperación para una extracción de contenido centrado y relevante, recomendado para la mayoría de los casos.", "Default User Role": "Rol predeterminado de los nuevos usuarios", "Delete": "Bo<PERSON>r", "Delete a model": "Borrar un modelo", "Delete All Chats": "<PERSON><PERSON><PERSON> todos los chats", "Delete All Models": "Bo<PERSON>r todos los modelos", "Delete chat": "Bo<PERSON>r chat", "Delete Chat": "<PERSON><PERSON><PERSON>", "Delete chat?": "¿Borrar el chat?", "Delete folder?": "¿<PERSON><PERSON><PERSON> carpeta?", "Delete function?": "Borrar la función?", "Delete Message": "<PERSON><PERSON><PERSON>", "Delete message?": "¿<PERSON><PERSON>r mensaje?", "Delete note?": "¿<PERSON><PERSON>r nota?", "Delete prompt?": "¿Bo<PERSON>r el prompt?", "delete this link": "Bo<PERSON>r este enlace", "Delete tool?": "¿Borrar la herramienta?", "Delete User": "<PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} Borrado", "Deleted {{name}}": "{{nombre}} <PERSON><PERSON><PERSON>", "Deleted User": "<PERSON><PERSON><PERSON>", "Deployment names are required for Azure OpenAI": "Azure OpenAI requiere nombres de implementación", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Describe tu Base de Conocimientos y sus objetivos", "Description": "Descripción", "Detect Artifacts Automatically": "Detectar Artefactos Automáticamente", "Dictate": "Dictar", "Didn't fully follow instructions": "No seguiste completamente las instrucciones", "Direct": "Directo", "Direct Connections": "Conexiones Directas", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Las Conexiones Directas permiten a los usuarios conectar a sus propios endpoints compatibles API OpenAI.", "Direct Connections settings updated": "Se actualizaron las configuraciones de las Conexiones Directas", "Direct Tool Servers": "Servidores de Herramientas Directos", "Disable Image Extraction": "Deshabilitar Extracción de Imágenes", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Desabilita la extracción de imágenes del pdf. Si está habilitado Usar LLM las imágenes se capturan automáticamente. Por defecto el valor es Falso (las imágenes se extraen).", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a function": "Descubrir Funciónes", "Discover a model": "Descubrir <PERSON>", "Discover a prompt": "Descubrir Prompts", "Discover a tool": "<PERSON><PERSON><PERSON><PERSON>", "Discover how to use Open WebUI and seek support from the community.": "Descubre cómo usar Open WebUI y busca Soporte Comunitario.", "Discover wonders": "Descubre Maravillas", "Discover, download, and explore custom functions": "Descubre, descarga y explora funciones personalizadas", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, descarga, y explora prompts personalizados", "Discover, download, and explore custom tools": "Descubre, descarga y explora herramientas personalizadas", "Discover, download, and explore model presets": "Descubre, descarga y explora modelos con preajustados", "Dismissible": "Desestimable", "Display": "Mostrar", "Display Emoji in Call": "Muestra Emojis en Llamada", "Display the username instead of You in the Chat": "Mostrar en el chat el nombre de usuario en lugar del genérico Tu", "Displays citations in the response": "Mostrar citas en la respuesta", "Dive into knowledge": "Sumérgete en el conocimiento", "Do not install functions from sources you do not fully trust.": "¡No instalar funciones de fuentes en las que que no se confíe totalmente!", "Do not install tools from sources you do not fully trust.": "¡No instalar herramientas de fuentes en las que no se confíe totalmente!", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling URL del servidor necesaria.", "Document": "Documento", "Document Intelligence": "Azure Doc Intelligence", "Document Intelligence endpoint and key required.": "Es neceario un endpoint y clave de Azure Document Intelligence.", "Documentation": "Documentación", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "no se realiza ninguna conexión externa y tus datos permanecen seguros alojados localmente en tu servidor.", "Domain Filter List": "Lista de Filtrado de Dominio", "Don't have an account?": "¿No tienes una cuenta?", "don't install random functions from sources you don't trust.": "¡no instalar funciones desconocidas de fuentes en las que no se confíe!", "don't install random tools from sources you don't trust.": "¡no instalar herramientas desconocidas de fuentes en las que no se confíe!", "Don't like the style": "¿No te gusta el estilo?", "Done": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download as SVG": "Descargar como SVG", "Download canceled": "Descarga cancelada", "Download Database": "Descargar Base de Datos", "Drag and drop a file to upload or select a file to view": "Arrastra y suelta un archivo para subirlo o selecciona uno para verlo", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to upload": "Arrastra aquí los archivos a subir.", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "p.ej. '30s','10m'. Unidades de tiempo válidas son 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "p.ej. \"j<PERSON>\" o un esquema JSON", "e.g. 60": "p.ej. 60", "e.g. A filter to remove profanity from text": "p.ej. Un filtro para eliminar malas palabras del texto", "e.g. en": "p.ej. es", "e.g. My Filter": "p.ej. <PERSON>", "e.g. My Tools": "p.ej. <PERSON><PERSON>", "e.g. my_filter": "p.ej. mi_filtro", "e.g. my_tools": "p.ej. mis_herram<PERSON>as", "e.g. pdf, docx, txt": "p.ej. pdf, docx, txt ...", "e.g. Tools for performing various operations": "p.ej. Herramientas para realizar varias operaciones", "e.g., 3, 4, 5 (leave blank for default)": "p.ej. , 3, 4, 5 ...", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "p. ej., en-US,ja-JP (dejar en blanco para detectar automáticamente)", "e.g., westus (leave blank for eastus)": "p.ej. ,oeste (dejar vacío para este)", "e.g.) en,fr,de": "p.ej. ) en,es,fr,de", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Editar Modelo en Arena", "Edit Channel": "Editar Canal", "Edit Connection": "Editar Conexión", "Edit Default Permissions": "<PERSON>ar <PERSON>deter<PERSON>", "Edit Memory": "<PERSON><PERSON>", "Edit User": "<PERSON><PERSON>", "Edit User Group": "Editar Grupo de Usuarios", "Eject": "Expulsar", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "Embarcate en aventuras", "Embedding": "Incrustación", "Embedding Batch Size": "Tamaño del Lote de Incrustación", "Embedding Model": "Modelo de Incrustación", "Embedding Model Engine": "Motor del Modelo de Incrustación", "Embedding model set to \"{{embedding_model}}\"": "Modelo de Incrustación configurado a \"{{embedding_model}}\"", "Enable API Key": "Habilitar Clave API", "Enable autocomplete generation for chat messages": "Habilitar generación de autocompletado para mensajes de chat", "Enable Code Execution": "Habilitar Ejecución de Código", "Enable Code Interpreter": "Habilitar Interprete de Código", "Enable Community Sharing": "Habilitar Compartir con la Comunidad", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Habilitar bloqueo de memoria (mlock) para prevenir que los datos del modelo se intercambien fuera de la RAM. Esta opción bloquea el conjunto de páginas de trabajo del modelo en RAM, asegurando que no se intercambiarán fuera a disco. Esto puede ayudar a mantener el rendimiento evitando fallos de página y asegurando un acceso rápido a los datos.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Habilitar Mapeado de Memoria (mmap) para cargar datos del modelo. Esta opción permite al sistema usar el almacenamiento del disco como una extensión de la RAM al tratar los archivos en disco como si estuvieran en la RAM. Esto puede mejorar el rendimiento del modelo al permitir un acceso más rápido a los datos. Sin embargo, puede no funcionar correctamente con todos los sistemas y puede consumir una cantidad significativa de espacio en disco.", "Enable Message Rating": "Habilitar Calificación de los Mensajes", "Enable Mirostat sampling for controlling perplexity.": "Algoritmo de decodificación de texto neuronal que controla activamente el proceso generativo para mantener la perplejidad del texto generado en un valor deseado. Previene las trampas de aburrimiento (por excesivas repeticiones) y de incoherencia (por generación de excesivo texto).", "Enable New Sign Ups": "Habilitar Registros de Nuevos Usuarios", "Enabled": "Habilitado", "Endpoint URL": "Endpoint URL", "Enforce Temporary Chat": "Forzar el uso de Chat Temporal", "Enhance": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Asegúrese de que su archivo CSV incluya 4 columnas en este orden: Nombre, Correo Electrónico, Contraseña, Rol.", "Enter {{role}} message here": "Ingresar mensaje {{role}} aquí", "Enter a detail about yourself for your LLMs to recall": "Ingresar detalles sobre ti para que los recuerden sus LLMs", "Enter a title for the pending user info overlay. Leave empty for default.": "Ingresar un título para la sobrecapa informativa de usuario pendiente. Dejar vacío para usar el predeterminado.", "Enter a watermark for the response. Leave empty for none.": "Ingresar una marca de agua para la respuesta. Dejalo vacío para ninguna", "Enter api auth string (e.g. username:password)": "Ingresar campo de autorización de la api (p.ej. nombre:contraseña)", "Enter Application DN": "Ingresar el DN de la Aplicación", "Enter Application DN Password": "Ingresar la Contraseña del DN de la Aplicación", "Enter Bing Search V7 Endpoint": "Ingresar el Endpoint de Bing Search V7", "Enter Bing Search V7 Subscription Key": "Ingresar la Clave de Suscripción de Bing Search V7", "Enter BM25 Weight": "Ingresar Valor para BM25 (0 totalmente léxica, 1 totalmente semántica", "Enter Bocha Search API Key": "Ingresar la Clave API de Bocha Search", "Enter Brave Search API Key": "Ingresar la Clave API de Brave Search", "Enter certificate path": "Ingresar la ruta del certificado", "Enter CFG Scale (e.g. 7.0)": "Ingresar escala CFG (p.ej., 7.0)", "Enter Chunk Overlap": "Ingresar Superposición de los Fragmentos", "Enter Chunk Size": "Ingresar el Tamaño del Fragmento", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Ingresar pares \"token:valor_sesgo\" separados por comas (ejemplo: 5432:100, 413:-100)", "Enter Config in JSON format": "Ingresar Config en formato JSON", "Enter content for the pending user info overlay. Leave empty for default.": "Ingresar contenido para la sobrecapa informativa de usuario pendiente. Dejar vacío para usar el predeterminado.", "Enter Datalab Marker API Key": "Ingresar Clave API de Datalab Marker", "Enter description": "Ingresar Descripción", "Enter Docling OCR Engine": "Ingresar Motor del OCR de Docling", "Enter Docling OCR Language(s)": "Ingresar Lenguaje/s del OCR de Docling", "Enter Docling Server URL": "Ingresar URL del Servidor Docling", "Enter Document Intelligence Endpoint": "Ingresar el Endpoint de Azure Document Intelligence", "Enter Document Intelligence Key": "Ingresar Clave de Azure Document Intelligence", "Enter domains separated by commas (e.g., example.com,site.org)": "Ingresar dominios separados por comas (p.ej., ejemplo.com,sitio.org)", "Enter Exa API Key": "Ingresar Clave API de Exa", "Enter External Document Loader API Key": "Ingresar Clave API del Cargador Externo de Documentos", "Enter External Document Loader URL": "Ingresar URL del Cargador Externo de Documentos", "Enter External Web Loader API Key": "Ingresar Clave API del Cargador Web Externo", "Enter External Web Loader URL": "Ingresar URL del Cargador Web Externo", "Enter External Web Search API Key": "Ingresar Clave API del Buscador Web Externo", "Enter External Web Search URL": "Ingresar URL del Buscador Web Externo", "Enter Firecrawl API Base URL": "Ingresar URL Base del API de Firecrawl", "Enter Firecrawl API Key": "Ingresar Clave del API de Firecrawl", "Enter Github Raw URL": "Ingresar URL Github en Bruto(raw)", "Enter Google PSE API Key": "Ingresar Clave API de Google PSE", "Enter Google PSE Engine Id": "Ingresa ID del Motor PSE de Google", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> (p.ej. 512x512)", "Enter Jina API Key": "Ingresar Clave API de Jina", "Enter Jupyter Password": "Ingresar Contraseña de Jupyter", "Enter Jupyter Token": "Ingresar Token de Jupyter", "Enter Jupyter URL": "Ingresar URL de Jupyter", "Enter Kagi Search API Key": "Ingresar Clave API de Kagi Search", "Enter Key Behavior": "Comportamiento de la Tecla de Envío", "Enter language codes": "Ingresar Códigos de Idioma", "Enter Mistral API Key": "Ingresar Clave API de Mistral", "Enter Model ID": "Ingresar ID del Modelo", "Enter model tag (e.g. {{modelTag}})": "Ingresar la etiqueta del modelo (p.ej. {{modelTag}})", "Enter Mojeek Search API Key": "Ingresar Clave API de Mojeek Search", "Enter name": "Ingresar Nombre", "Enter New Password": "Ingresar Contraseña Nueva", "Enter Number of Steps (e.g. 50)": "Ingresar Número de <PERSON>s (p.ej., 50)", "Enter Perplexity API Key": "Ingresar Clave API de Perplexity", "Enter Playwright Timeout": "Ingresar límite de tiempo de espera de Playwright", "Enter Playwright WebSocket URL": "Ingresar URL de WebSocket de Playwright", "Enter proxy URL (e.g. **************************:port)": "Ingresar URL del proxy (p.ej. **************************:port)", "Enter reasoning effort": "Ingresar esfuerzo de razonamiento", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON><PERSON> (p.ej., <PERSON><PERSON>r a)", "Enter Scheduler (e.g. Karras)": "<PERSON>g<PERSON><PERSON> (p.ej., <PERSON><PERSON><PERSON>)", "Enter Score": "Ingresar Puntuación", "Enter SearchApi API Key": "Ingresar Clave API de SearchApi", "Enter SearchApi Engine": "Ingresar Motor de SearchApi", "Enter Searxng Query URL": "Ingresar URL de la consulta Searxng", "Enter Seed": "<PERSON><PERSON><PERSON><PERSON>", "Enter SerpApi API Key": "Ingresar Clave API de SerpApi", "Enter SerpApi Engine": "Ingresar Motor de SerpApi", "Enter Serper API Key": "Ingresar Clave API de Serper", "Enter Serply API Key": "Ingresar Clave API de Serply", "Enter Serpstack API Key": "Ingresar Clave API de Serpstack", "Enter server host": "Ingresar host del servidor", "Enter server label": "Ingresar etiqueta del servidor", "Enter server port": "Ingresar puerto del servidor", "Enter Sougou Search API sID": "Ingresar Sougou Search API sID", "Enter Sougou Search API SK": "Ingresar Sougou Search API SK", "Enter stop sequence": "Ingresar secuencia de parada", "Enter system prompt": "Ingresar Prompt del sistema", "Enter system prompt here": "Ingresa aquí el prompt del sistema", "Enter Tavily API Key": "Ingresar Clave API de Tavily", "Enter Tavily Extract Depth": "Ingresar parámetro de Extract Depth de Taviliy", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Ingresar URL pública de WebUI. Esta URL se usará para generar enlaces en las notificaciones.", "Enter the URL of the function to import": "Ingresar la URL de la función a importar", "Enter the URL to import": "Ingresar la URL a importar", "Enter Tika Server URL": "Ingresar URL del servidor Tika", "Enter timeout in seconds": "Ingresar tiempo límite de espera en segundos", "Enter to Send": "'Enter' para Enviar", "Enter Top K": "Ingresar Top K", "Enter Top K Reranker": "Ingresar Top K Reclasificador", "Enter URL (e.g. http://127.0.0.1:7860/)": "Ingresar URL (p.ej., http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Ingresar URL (p.ej., http://localhost:11434)", "Enter Yacy Password": "Ingresar Contraseña de Yacy", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Ingresar URL de Yacy (p.ej. http://yacy.ejemplo.com:8090)", "Enter Yacy Username": "Ingresar Nombre de Usuario de Yacy", "Enter your current password": "Ingresa tu contraseña actual", "Enter Your Email": "Ingresa tu correo electrónico", "Enter Your Full Name": "Ingresa su nombre completo", "Enter your message": "Ingresa tu mensaje", "Enter your name": "Ingresa tu nombre", "Enter Your Name": "Ingresa Tu Nombre", "Enter your new password": "Ingresa tu contraseña nueva", "Enter Your Password": "Ingresa tu contraseña", "Enter Your Role": "Ingresa tu rol", "Enter Your Username": "Ingresa tu nombre de usuario", "Enter your webhook URL": "Ingresa tu URL de webhook", "Error": "Error", "ERROR": "ERROR", "Error accessing Google Drive: {{error}}": "Error accediendo a Google Drive: {{error}}", "Error accessing media devices.": "Error accediendo a dispositivos de medios.", "Error starting recording.": "Error al comenzar la grabación.", "Error unloading model: {{error}}": "Error subiendo el modelo: {{error}}", "Error uploading file: {{error}}": "Error subiendo el archivo: {{error}}", "Evaluations": "Evaluaciones", "Exa API Key": "Clave API de Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Ejemplo: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Ejemplo: TODOS", "Example: mail": "Ejemplo: correo", "Example: ou=users,dc=foo,dc=example": "Ejemplo: ou=usuarios,dc=foo,dc=ejemplo", "Example: sAMAccountName or uid or userPrincipalName": "Ejemplo: sAMNombreCuenta o uid o userNombrePrincipal", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Excedido el número de accesos de usuarios en tu licencia. Por favor, contacta con soporte para aumentar el número de accesos.", "Exclude": "Excluir", "Execute code for analysis": "Ejecutar código para análisis", "Executing **{{NAME}}**...": "Ejecutando **{{NAME}}**...", "Expand": "Expandir", "Experimental": "Experimental", "Explain": "Explicar", "Explore the cosmos": "Explora el cosmos", "Export": "Exportar", "Export All Archived Chats": "Exportar Todos los Chats Archivados", "Export All Chats (All Users)": "Exportar Todos los Chats (Todos los Usuarios)", "Export chat (.json)": "Exportar chat (.json)", "Export Chats": "Exportar Chats", "Export Config to JSON File": "Exportar Configuración a archivo JSON", "Export Functions": "Exportar Funciones", "Export Models": "Exportar Modelos", "Export Presets": "Exportar Pre<PERSON>ustes", "Export Prompt Suggestions": "Exportar Sugerencias de Prompt", "Export Prompts": "Exportar Prompts", "Export to CSV": "Exportar a CSV", "Export Tools": "Exportar Herramientas", "External": "Externo", "External Document Loader URL required.": "LA URL del Cargador Externo de Documentos es requerida.", "External Task Model": "Modelo Externo de Herramientas", "External Web Loader API Key": "Clave API del Cargador Web Externo", "External Web Loader URL": "URL del Cargador Web Externo", "External Web Search API Key": "Clave API del Buscador Web Externo", "External Web Search URL": "URL del Buscador Web Externo", "Failed to add file.": "Fallo al  añadir el archivo.", "Failed to connect to {{URL}} OpenAPI tool server": "Fallo al conectar al servidor de herramientas {{URL}}", "Failed to copy link": "Fallo al copiar enlace", "Failed to create API Key.": "Fallo al crear la Clave API.", "Failed to delete note": "Fallo al eliminar nota", "Failed to fetch models": "Fallo al obtener los modelos", "Failed to load file content.": "Fallo al cargar el contenido del archivo", "Failed to read clipboard contents": "Fallo al leer el contenido del portapapeles", "Failed to save connections": "Fallo al grabar las conexiones", "Failed to save models configuration": "Fallo al guardar la configuración de los modelos", "Failed to update settings": "Fallo al actualizar los ajustes", "Failed to upload file.": "Fallo al subir el archivo.", "Features": "Características", "Features Permissions": "Permisos de las Características", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "Detalle de la Opinión", "Feedback History": "Historia de la Opiniones", "Feedbacks": "Opiniones", "Feel free to add specific details": "Añade libremente detalles específicos", "File": "Archivo", "File added successfully.": "Archivo añadido correctamente.", "File content updated successfully.": "Contenido del archivo actualizado correctamente.", "File Mode": "Modo de Archivo", "File not found.": "Archivo no encontrado.", "File removed successfully.": "Archivo eliminado correctamente.", "File size should not exceed {{maxSize}} MB.": "Tamaño del archivo no debe exceder {{maxSize}} MB.", "File Upload": "Subir Archivo", "File uploaded successfully": "Archivo subido correctamente", "Files": "Archivos", "Filter is now globally disabled": "El filtro ahora está desactivado globalmente", "Filter is now globally enabled": "El filtro ahora está habilitado globalmente", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Se detectó suplantación de huellas: No se pueden usar las iniciales como avatar. Se establece la imagen de perfil predeterminada.", "Firecrawl API Base URL": "URL Base de API de Firecrawl", "Firecrawl API Key": "Clave de API de Firecrawl", "Fluidly stream large external response chunks": "Transmisión fluida de fragmentos de grandes respuestas externas", "Focus chat input": "Enfocar campo de chat", "Folder deleted successfully": "Carpeta eliminada correctamente", "Folder name cannot be empty.": "El nombre de la carpeta no puede estar vacío", "Folder name updated successfully": "Nombre de la carpeta actualizado correctamente", "Follow up": "Segu<PERSON><PERSON><PERSON>", "Follow Up Generation": "Seguimiento de la Generación", "Follow Up Generation Prompt": "Seguimiento de la Generación del Prompt", "Follow-Up Auto-Generation": "Seguimiento Auto-Generación", "Followed instructions perfectly": "Siguió las instrucciones perfectamente", "Force OCR": "Forzar OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Forzar OCR en todas las páginas del PDF. Puede empeorar el resultado en PDFs que ya tengan una buena capa de texto. El valor predeterminado es desactivado (no forzar)", "Forge new paths": "Forjar nuevos caminos", "Form": "Formulario", "Format your variables using brackets like this:": "Formatea tus variables usando corchetes así:", "Forwards system user session credentials to authenticate": "Reenvío de las credenciales de la sesión del usuario del sistema para autenticación", "Full Context Mode": "<PERSON><PERSON>mpleto", "Function": "Función", "Function Calling": "Modo de Llamada a Funciones (Herramientas)", "Function created successfully": "Función creada correctamente", "Function deleted successfully": "Función borrada correctamente", "Function Description": "Descripción de la Función", "Function ID": "ID de la Función", "Function imported successfully": "Función importada correctamente", "Function is now globally disabled": "La Función ahora está deshabilitada globalmente", "Function is now globally enabled": "La Función ahora está habilitada globalmente", "Function Name": "Nombre de la Función", "Function updated successfully": "Función actualizada correctamente", "Functions": "Funciones", "Functions allow arbitrary code execution.": "Las Funciones habilitan la ejecución de código arbitrario.", "Functions imported successfully": "Funciones importadas correctamente", "Gemini": "Gemini", "Gemini API Config": "Config API Gemini", "Gemini API Key is required.": "Se requiere Clave API de Gemini.", "General": "General", "Generate": "Generar", "Generate an image": "Generar una imagen", "Generate Image": "Generar imagen", "Generate prompt pair": "Generar par de prompts", "Generating search query": "Generando consulta de búsqueda", "Generating...": "Generando", "Get started": "Empezar", "Get started with {{WEBUI_NAME}}": "<PERSON><PERSON>zar con {{WEBUI_NAME}}", "Global": "Global", "Good Response": "Buena Respuesta", "Google Drive": "Google Drive", "Google PSE API Key": "Clave API de Google PSE", "Google PSE Engine Id": "ID del Motor PSE de Google", "Group created successfully": "Grupo creado correctamente", "Group deleted successfully": "Grupo eliminado correctamente", "Group Description": "Descripción del Grupo", "Group Name": "Nombre del Grupo", "Group updated successfully": "Grupo actualizado correctamente", "Groups": "Grupos", "Haptic Feedback": "Realimentación Háptica", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "¡Ayúdanos a crear la mejor tabla clasificatoria comunitaria compartiendo tus comentarios!", "Hex Color": "Color Hex", "Hex Color - Leave empty for default color": "Color Hex - Deja vacío para el color predeterminado", "Hide": "Esconder", "Hide from Sidebar": "Ocultar Panel Lateral", "Hide Model": "Ocultar Modelo", "High Contrast Mode": "Modo Alto Contraste", "Home": "<PERSON><PERSON>o", "Host": "Host", "How can I help you today?": "¿Cómo puedo ayudarte hoy?", "How would you rate this response?": "¿Cómo calificarías esta respuesta?", "HTML": "HTML", "Hybrid Search": "Búsqueda Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON> que he leído y entiendo las implicaciones de mi acción. Soy consciente de los riesgos asociados con la ejecución de código arbitrario y he verificado la confiabilidad de la fuente.", "ID": "ID", "iframe Sandbox Allow Forms": "iframe Sandbox Allow Forms", "iframe Sandbox Allow Same Origin": "iframe Sandbox Allow Same Origin", "Ignite curiosity": "Encender la curiosidad", "Image": "Imagen", "Image Compression": "Compresión de Imagen", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Generación de Imagen", "Image Generation (Experimental)": "Generación de Imagen (experimental)", "Image Generation Engine": "Motor de Generación de Imagen", "Image Max Compression Size": "Tamaño Máximo de Compresión de Imagen", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Prompt para Generación de Imagen", "Image Prompt Generation Prompt": "Prompt para la Generación de Imagen", "Image Settings": "Configuración de Imágen", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import": "Importar", "Import Chats": "Importar Chats", "Import Config from JSON File": "Importar Config desde Archivo JSON", "Import From Link": "Importar des<PERSON>", "Import Functions": "Importar Funciones", "Import Models": "Importar Modelos", "Import Notes": "Importar Notas", "Import Presets": "Importar <PERSON>", "Import Prompt Suggestions": "Importar Prompts Sugeridos", "Import Prompts": "Importar Prompts", "Import Tools": "Importar <PERSON>", "Include": "Incluir", "Include `--api-auth` flag when running stable-diffusion-webui": "Incluir el señalizador `--api-auth` al ejecutar stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Incluir el señalizador `--api` al ejecutar stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Influye en la rápidez de respuesta a la realimentación desde el texto generado. Una tasa de aprendizaje más baja resulta en un ajustado más lento, mientras que una tasa de aprendizaje más alta hará que el algoritmo sea más reactivo.", "Info": "Información", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Inyecta el contenido completo como contexto para un procesado comprensivo, recomendado para consultas complejas.", "Input commands": "Ingresar comandos", "Install from Github URL": "Instalar desde la URL de Github", "Instant Auto-Send After Voice Transcription": "AutoEnvio Instantaneo tras la Transcripción de Voz", "Integration": "Integración", "Interface": "Interface", "Invalid file content": "Contenido de archivo inválido", "Invalid file format.": "Formato de archivo inválido.", "Invalid JSON file": "Archivo JSON inválido", "Invalid Tag": "Etiqueta <PERSON>álid<PERSON>", "is typing...": "está escribiendo...", "January": "<PERSON><PERSON>", "Jina API Key": "Clave API de Jina", "join our Discord for help.": "unete a nuestro Discord para ayuda.", "JSON": "JSON", "JSON Preview": "Prevista del JSON", "July": "<PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "Autenticación de Jupyter", "Jupyter URL": "URL de Jupyter", "JWT Expiration": "Expiración del JSON Web Token (JWT)", "JWT Token": "JSON Web Token", "Kagi Search API Key": "Clave API de Kagi Search", "Keep in Sidebar": "", "Key": "Clave", "Keyboard shortcuts": "Atajos de teclado", "Knowledge": "Conocimiento", "Knowledge Access": "Acceso a Conocimiento", "Knowledge created successfully.": "Conocimiento creado correctamente.", "Knowledge deleted successfully.": "Conocimiento eliminado correctamente.", "Knowledge Public Sharing": "Compartir Conocimiento Públicamente", "Knowledge reset successfully.": "Conocimiento restablecido correctamente.", "Knowledge updated successfully": "Conocimiento actualizado correctamente.", "Kokoro.js (Browser)": "Kokoro.js (Navegador)", "Kokoro.js Dtype": "Kokoro.js DType", "Label": "Etiqueta", "Landing Page Mode": "<PERSON><PERSON>a Inicial", "Language": "Idioma", "Language Locales": "Configuración regional del Idioma", "Languages": "Idiomas", "Last Active": "Última Actividad", "Last Modified": "Último Modificación", "Last reply": "Última Respuesta", "LDAP": "LDAP", "LDAP server updated": "Servidor LDAP actualizado", "Leaderboard": "Tabla Clasificatoria", "Learn more about OpenAPI tool servers.": "Saber más sobre los servidores de herramientas OpenAPI", "Leave empty for no compression": "", "Leave empty for unlimited": "Dejar vacío para ilimitado", "Leave empty to include all models from \"{{url}}\" endpoint": "Dejar vacío para incluir todos los modelos desde el endpoint \"{{url}}\"", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Dejar vacío para incluir todos los modelos desde el endpoint \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Dejar vacío para incluir todos los modelos desde el endpoint \"{{url}}/models\"", "Leave empty to include all models or select specific models": "Dejar vacío para incluir todos los modelos o Seleccionar modelos específicos", "Leave empty to use the default prompt, or enter a custom prompt": "Dejar vacío para usar el prompt predeterminado, o Ingresar un prompt personalizado", "Leave model field empty to use the default model.": "Dejar vacío el campo modelo para usar el modelo predeterminado.", "License": "Licencia", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Escuchando...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Los LLMs pueden cometer errores. Verifica la información importante.", "Loader": "Cargador", "Loading Kokoro.js...": "Cargando Kokoro.js...", "Local": "Local", "Local Task Model": "Modelo Local para Tarea", "Location access not allowed": "Sin acceso a la Ubicación", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Creado por la Comunidad Open-WebUI", "Make password visible in the user interface": "Hacer visible la contraseña en la interface del usuario.", "Make sure to enclose them with": "Asegúrate de delimitarlos con", "Make sure to export a workflow.json file as API format from ComfyUI.": "Asegúrate de exportar un archivo workflow.json en formato API desde ComfyUI.", "Manage": "Gestionar", "Manage Direct Connections": "Gestionar Conexiones Directas", "Manage Models": "Gestionar Modelos", "Manage Ollama": "Gest<PERSON><PERSON>", "Manage Ollama API Connections": "Gestionar Conexiones API de Ollama", "Manage OpenAI API Connections": "Gestionar Conexiones API de OpenAI", "Manage Pipelines": "Gestionar Tuberías", "Manage Tool Servers": "Gestionar Servidores de Herramientas", "March": "<PERSON><PERSON>", "Markdown": "<PERSON><PERSON>", "Max Speakers": "Max Interlocutores", "Max Upload Count": "Número Max de Subidas", "Max Upload Size": "Tamaño Max de Subidas", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Se puede descargar un máximo de 3 modelos simultáneamente. <PERSON><PERSON> favor, reinténtelo más tarde.", "May": "Mayo", "Memories accessible by LLMs will be shown here.": "Las memorias accesibles por los LLMs se mostrarán aquí.", "Memory": "Memoria", "Memory added successfully": "Memoria añadida correctamente", "Memory cleared successfully": "Memoria liberada correctamente", "Memory deleted successfully": "Memoria borrada correctamente", "Memory updated successfully": "Memoria actualizada correctamente", "Merge Responses": "Fusionar Respuestas", "Merged Response": "Respuesta combinada", "Message rating should be enabled to use this feature": "Para usar esta función debe estar habilitada la calificación de mensajes", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Los mensajes que envíe después de la creación del enlace no se compartirán. Los usuarios con la URL del enlace podrán ver el chat compartido.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (personal)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (trabajo/estudio)", "Mistral OCR": "OCR Mistral", "Mistral OCR API Key required.": "Clave API de Mistral OCR requerida", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Modelo '{{model<PERSON><PERSON>}}' se ha descargado correctamente.", "Model '{{modelTag}}' is already in queue for downloading.": "Modelo '{{modelTag}}' ya está en cola para descargar.", "Model {{modelId}} not found": "Modelo {{modelId}} no encontrado", "Model {{modelName}} is not vision capable": "Modelo {{modelName}} no esta capacitado para visión", "Model {{name}} is now {{status}}": "<PERSON>o {{name}} est<PERSON> ahora {{status}}", "Model {{name}} is now hidden": "<PERSON>o {{name}} está ahora oculto", "Model {{name}} is now visible": "Modelo {{name}} está ahora visible", "Model accepts file inputs": "Modelo acepta entrada de archivos", "Model accepts image inputs": "Modelo acepta entrada de imagenes", "Model can execute code and perform calculations": "Modelo puedo ejecutar código y realizar cálculos", "Model can generate images based on text prompts": "Modelo puede generar imágenes basadas en prompts de texto", "Model can search the web for information": "Modelo puede buscar información en la web", "Model created successfully!": "¡Modelo creado correctamente!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Detectada ruta del sistema al modelo. Para actualizar se requiere el nombre corto del modelo, no se puede continuar.", "Model Filtering": "Filtrado de modelos", "Model ID": "ID Modelo", "Model IDs": "IDs Modelo", "Model Name": "Nombre Modelo", "Model not selected": "Modelo no seleccionado", "Model Params": "Paráms Modelo", "Model Permissions": "<PERSON><PERSON><PERSON>", "Model unloaded successfully": "<PERSON><PERSON> correctamente", "Model updated successfully": "Modelo actualizado correctamente", "Model(s) do not support file upload": "Modelo/s no soportan subida de archivos", "Modelfile Content": "Contenido del Modelfile", "Models": "Modelos", "Models Access": "Acceso Modelos", "Models configuration saved successfully": "Configuración de Modelos guardada correctamente", "Models Public Sharing": "Compartir Modelos Públicamente", "Mojeek Search API Key": "Clave API de Mojeek Search", "more": "más", "More": "Más", "My Notes": "<PERSON><PERSON>", "Name": "Nombre", "Name your knowledge base": "Nombra tu base de conocimientos", "Native": "Nativo", "New Chat": "Nuevo Chat", "New Folder": "Nueva Carpeta", "New Function": "Nueva Función", "New Note": "Nueva Nota", "New Password": "Nueva Contraseña", "New Tool": "Nueva Herramienta", "new-channel": "nuevo-canal", "Next message": "Siguiente mensaje", "No chats found for this user.": "No se encontró ningún chat de este usuario", "No chats found.": "No se encontró ningún chat", "No content": "Sin contenido", "No content found": "No se encontró contenido", "No content found in file.": "No se encontró contenido en el archivo", "No content to speak": "No hay contenido para hablar", "No distance available": "No hay distancia disponible", "No feedbacks found": "No se encontraron comentarios", "No file selected": "No se seleccionó archivo", "No groups with access, add a group to grant access": "No hay grupos con acceso, añade un grupo para otorgar acceso", "No HTML, CSS, or JavaScript content found.": "No se encontró contenido HTML, CSS, o JavaScript.", "No inference engine with management support found": "No se encontró un motor de inferencia que soporte gestión", "No knowledge found": "No se encontró ningún conocimiento", "No memories to clear": "No hay memorias para borrar", "No model IDs": "No hay IDs de modelo", "No models found": "No se encontraron modelos", "No models selected": "No se seleccionaron modelos", "No Notes": "<PERSON>", "No results found": "No se encontraron resultados", "No search query generated": "No se generó ninguna consulta de búsqueda", "No source available": "No hay fuente disponible", "No users were found.": "No se encontraron usuarios.", "No valves to update": "No hay válvulas para actualizar", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "No es correcto en todos los aspectos", "Not helpful": "No aprovechable", "Note deleted successfully": "Nota eliminada correctamente", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Si estableces una puntuación mínima, la búsqueda sólo devolverá documentos con una puntuación mayor o igual a la puntuación mínima establecida.", "Notes": "Notas", "Notification Sound": "Notificación Sonora", "Notification Webhook": "Notificación Enganchada (webhook)", "Notifications": "Notificaciones", "November": "Noviembre", "OAuth ID": "OAuth ID", "October": "Octubre", "Off": "Desactivado", "Okay, Let's Go!": "Vale, ¡Vamos!", "OLED Dark": "Oscuro OLED", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "Ajustes de la API de Ollama actualizados", "Ollama Version": "Versión de Ollama", "On": "Activado", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Solo activo cuando la opción  \"Pegar el Texto Largo como Archivo\" está activada.", "Only active when the chat input is in focus and an LLM is generating a response.": "Solo activo cuando el foco está en la entrada del chat y el LLM está generando una respuesta.", "Only alphanumeric characters and hyphens are allowed": "Sólo están permitidos caracteres alfanuméricos y guiones", "Only alphanumeric characters and hyphens are allowed in the command string.": "Sólo están permitidos en la cadena de comandos caracteres alfanuméricos y guiones.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Solo se pueden editar las colecciones, para añadir/editar documentos hay que crear una nueva base de conocimientos", "Only markdown files are allowed": "Solo están permitidos archivos markdown", "Only select users and groups with permission can access": "Solo pueden acceder los usuarios y grupos con permiso", "Oops! Looks like the URL is invalid. Please double-check and try again.": "¡vaya! Parece que la URL es inválida. Por favor, revisala y reintenta de nuevo.", "Oops! There are files still uploading. Please wait for the upload to complete.": "¡vaya! Todavía hay archivos subiendose. Por favor, espera a que se complete la subida.", "Oops! There was an error in the previous response.": "¡vaya! Hubo un error en la respuesta previa.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "¡vaya! Estás usando un método no soportado (solo interface frontal-frontend). Por favor sirve WebUI desde el interface trasero (servidor backend).", "Open file": "Abrir archivo", "Open in full screen": "Abrir en pantalla completa", "Open modal to configure connection": "Abrir modal para configurar la conexión", "Open new chat": "Abrir nuevo chat", "Open WebUI can use tools provided by any OpenAPI server.": "Open-WebUI puede usar herramientas proporcionadas por cualquier servidor OpenAPI", "Open WebUI uses faster-whisper internally.": "Open-WebUI usa faster-whisper internamente.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open-WebUI usa SpeechT5 y la incrustración de locutores de CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "La versión de Open-WebUI (v{{OPEN_WEBUI_VERSION}}) es inferior a la versión  (v{{REQUIRED_VERSION}}) requerida", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Config API OpenAI", "OpenAI API Key is required.": "Clave API de OpenAI requerida.", "OpenAI API settings updated": "Ajustes de API OpenAI actualizados", "OpenAI URL/Key required.": "URL/Clave de OpenAI requerida.", "openapi.json URL or Path": "URL o Ruta a openapi.json", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "Opciones para usar modelos locales en la descripción de imágenes. Los parámetros se refieren a modelos alojados en HugginFace. Esta opción es mutuamente excluyente con \"picture_description_api\".", "or": "o", "Organize your users": "Organiza tus usuarios", "Other": "<PERSON><PERSON>", "OUTPUT": "SALIDA", "Output format": "Formato de salida", "Output Format": "", "Overview": "Vista General", "page": "p<PERSON><PERSON><PERSON>", "Paginate": "<PERSON><PERSON><PERSON>", "Parameters": "Parámetros", "Password": "Contraseña", "Paste Large Text as File": "Pegar el Texto Largo como Archivo", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Extraer imágenes del PDF (OCR)", "pending": "pendiente", "Pending": "Pendiente", "Pending User Overlay Content": "Contenido de la SobreCapa Usuario Pendiente", "Pending User Overlay Title": "Título de la SobreCapa Usuario Pendiente", "Permission denied when accessing media devices": "Permiso denegado accediendo a los dispositivos", "Permission denied when accessing microphone": "Permiso denegado accediendo al micrófono", "Permission denied when accessing microphone: {{error}}": "Permiso denegado accediendo al micrófono: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Perplexity API Key": "Clave API de Perplexity", "Perplexity Model": "Perplexity Modelo", "Perplexity Search Context Usage": "Perplexity Usar Busqueda en Contexto", "Personalization": "Personalización", "Picture Description API Config": "Config API Descripción de Imagen", "Picture Description Local Config": "Config Descripción Imagen Local", "Picture Description Mode": "Modo Descripción de Imagen", "Pin": "<PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Pioneer insights": "Descubrir nuevas perspectivas", "Pipeline deleted successfully": "Tubería borrada correctamente", "Pipeline downloaded successfully": "Tubería descargada correctamente", "Pipelines": "Tuberías", "Pipelines Not Detected": "<PERSON><PERSON><PERSON> Tuberías (Pipelines) No Detectado", "Pipelines Valves": "Válvulas de Tuberías", "Plain text (.md)": "Texto plano (.md)", "Plain text (.txt)": "Texto plano (.txt)", "Playground": "Zona de Pruebas", "Playwright Timeout (ms)": "Tiempo Límite de Espera (ms) de Playwright", "Playwright WebSocket URL": "URL de WebSocket de Playwright", "Please carefully review the following warnings:": "Por favor revisar cuidadosamente los siguientes avisos:", "Please do not close the settings page while loading the model.": "Por favor no cerrar la página de ajustes mientras se está descargando el modelo.", "Please enter a prompt": "Por favor ingresar un prompt", "Please enter a valid path": "Por favor, ingresa una ruta válida", "Please enter a valid URL": "Por favor, ingresa una URL válida", "Please fill in all fields.": "Por favor rellenar todos los campos.", "Please select a model first.": "Por favor primero seleccionar un modelo.", "Please select a model.": "Por favor seleccionar un modelo.", "Please select a reason": "Por favor seleccionar un motivo", "Port": "Puerto", "Positive attitude": "<PERSON><PERSON><PERSON>", "Prefix ID": "prefijo ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "El prefijo ID se utiliza para evitar conflictos con otras conexiones al añadir un prefijo a los IDs de modelo, dejar vacío para deshabilitarlo", "Prevent file creation": "Prevenir la creación de archivos", "Preview": "Previsualización", "Previous 30 days": "30 días previos", "Previous 7 days": "7 días previos", "Previous message": "<PERSON><PERSON><PERSON>", "Private": "Privado", "Profile Image": "Imagen del Perfil", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (p.ej. <PERSON><PERSON>nta<PERSON> una cosa divertida sobre el Imperio Romano)", "Prompt Autocompletion": "Autocompletado del Prompt", "Prompt Content": "Contenido del Prompt", "Prompt created successfully": "Prompt creado exitosamente", "Prompt suggestions": "Prompts Suger<PERSON>s", "Prompt updated successfully": "Prompt actualizado correctamente", "Prompts": "Prompts", "Prompts Access": "Acceso a Prompts", "Prompts Public Sharing": "Compartir Prompts Públicamente", "Public": "Público", "Pull \"{{searchValue}}\" from Ollama.com": "Extraer \"{{searchValue}}\" desde Ollama.com", "Pull a model from Ollama.com": "Extraer un modelo desde Ollama.com", "Query Generation Prompt": "Prompt para la Consulta de Generación", "RAG Template": "Plantilla del RAG", "Rating": "Calificación", "Re-rank models by topic similarity": "Reclasificar modelos por similitud temática", "Read": "<PERSON><PERSON>", "Read Aloud": "Leer en voz alta", "Reason": "Razonamiento", "Reasoning Effort": "Esfuerzo del Razonamiento", "Record": "<PERSON><PERSON><PERSON>", "Record voice": "Grabar voz", "Redirecting you to Open WebUI Community": "Redireccionando a la Comunidad Open-WebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Reduce la probabilidad de generación sin sentido. Un valor más alto (p.ej. 100) dará respuestas más diversas, mientras que un valor más bajo (p.ej. 10) será más conservador.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Referir a ti mismo como \"Usuario\" (p.ej. \"Usuario está aprendiendo Español\")", "References from": "Referencias desde", "Refused when it shouldn't have": "Rechazado cuando no debería haberlo hecho", "Regenerate": "<PERSON><PERSON><PERSON>", "Reindex": "Reindexar", "Reindex Knowledge Base Vectors": "Reindexar Base Vectorial de Conocimiento", "Release Notes": "Notas de la Versión", "Releases": "Versiones", "Relevance": "Relevancia", "Relevance Threshold": "Umbral de Relevancia", "Remove": "Eliminar", "Remove {{MODELID}} from list.": "Eliminar {{MODELID}} de la lista.", "Remove Model": "Eliminar Modelo", "Remove this tag from list": "Eliminar esta etiquera de la lista", "Rename": "Renombrar", "Reorder Models": "Reordenar <PERSON>", "Reply in Thread": "Responder en Hilo", "Reranking Engine": "Motor de Reclasificación", "Reranking Model": "Modelo de Reclasificación", "Reset": "Reiniciar", "Reset All Models": "Reiniciar Todos los Modelos", "Reset Upload Directory": "Reiniciar Directorio de Subidas", "Reset Vector Storage/Knowledge": "Reiniciar Almacenamiento de Vectores/Conocimiento", "Reset view": "Reiniciar Vista", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Las notificaciones de respuesta no pueden activarse ya que los permisos del sitio web han sido denegados. Por favor, comprueba la configuración de tu navegador para otorgar el permiso necesario.", "Response splitting": "Particionado de Respuesta", "Response Watermark": "Marca de Agua en Respuesta", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "Recuperación", "Retrieval Query Generation": "Consulta de Generación de Recuperación", "Rich Text Input for Chat": "Entrada de Texto Enriquecido para el Chat", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Save": "Guardar", "Save & Create": "Guardar y Crear", "Save & Update": "Guardar y Actualizar", "Save As Copy": "Guardar como Copia", "Save Tag": "Guardar <PERSON>a", "Saved": "Guardado", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Ya no está soportado guardar registros de chat directamente en el almacenamiento del navegador. Por favor, dedica un momento a descargar y eliminar tus registros de chat pulsando en el botón de abajo. No te preocupes, puedes re-importar fácilmente tus registros desde las opciones de configuración", "Scroll On Branch Change": "Desplazamiento al Cambiar Rama", "Search": "Buscar", "Search a model": "Buscar un Modelo", "Search Base": "Busqueda Base", "Search Chats": "Buscar Chats", "Search Collection": "<PERSON><PERSON>", "Search Filters": "Buscar Filtros", "search for tags": "Buscar por etiquetas", "Search Functions": "Buscar Funciones", "Search Knowledge": "Buscar Conocimiento", "Search Models": "Buscar Modelos", "Search options": "Opciones de Búsqueda", "Search Prompts": "Buscar Prompts", "Search Result Count": "Número de resultados de la búsqueda", "Search the internet": "Buscar en internet", "Search Tools": "Buscar Herramientas", "SearchApi API Key": "Clave API de SearchApi", "SearchApi Engine": "Motor SearchApi", "Searched {{count}} sites": "{{count}} sitios buscados", "Searching \"{{searchQuery}}\"": "Buscando \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Buscando \"{{searchQuery}}\" en Conocimiento", "Searching the web...": "Buscando en la web...", "Searxng Query URL": "Searxng URL de Consulta", "See readme.md for instructions": "Ver readme.md para instrucciones", "See what's new": "Ver las novedades", "Seed": "<PERSON><PERSON>", "Select a base model": "Seleccionar un modelo base", "Select a engine": "Seleccionar un motor", "Select a function": "Seleccionar una función", "Select a group": "Seleccionar un grupo", "Select a model": "Selecciona un modelo", "Select a pipeline": "Seleccionar una tubería", "Select a pipeline url": "Seleccionar una url de tubería", "Select a tool": "Seleccioanr una herramienta", "Select an auth method": "Seleccionar un método de autentificación", "Select an Ollama instance": "Seleccionar una instancia de Ollama", "Select Engine": "Seleccionar Motor", "Select Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select only one model to call": "Seleccionar sólo un modelo a llamar", "Selected model(s) do not support image inputs": "Modelo(s) seleccionado(s) no admiten entradas de imagen", "Semantic distance to query": "Distancia semántica a la consulta", "Send": "Enviar", "Send a Message": "Enviar un Mensaje", "Send message": "<PERSON><PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia en la solicitud de transmisión la opción: `{ include_usage: true }`.\nSi se activa, los proveedores que soporten esta función devolverán en la respuesta información de uso de los token.", "September": "Septiembre", "SerpApi API Key": "Clave API de SerpApi", "SerpApi Engine": "Motor de SerpApi", "Serper API Key": "Clave API de Serper", "Serply API Key": "Clave API de Serply", "Serpstack API Key": "Clave API de Serpstack", "Server connection verified": "Conexión al servidor verificada", "Set as default": "Establecer como Predeterminado", "Set CFG Scale": "Establecer la Escala CFG", "Set Default Model": "Establecer Modelo Predeterminado", "Set embedding model": "Establecer Modelo de Incrustación", "Set embedding model (e.g. {{model}})": "Establecer Modelo para Incrustación (p.ej. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Establecer Modelo para Reclasificación (p.ej. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON>", "Set Scheduler": "Establecer Programador", "Set Steps": "E<PERSON><PERSON>", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Establece el número de capas, que deben cargarse en la GPU. Aumentar este valor puede mejorar significativamente el rendimiento de los modelos optimizados para aceleración en GPU, pero también puede consumir más energía y recursos de la GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Establece el número de hilos de trabajo utilizados para el computo. Esta opción controla cuántos hilos son usados para procesar solicitudes entrantes concurrentes. Aumentar este valor puede mejorar el rendimiento bajo cargas de trabajo de alta concurrencia, pero también puede consumir más recursos de la CPU.", "Set Voice": "E<PERSON><PERSON> la voz", "Set whisper model": "Establecer modelo whisper (transcripción)", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Establece un sesgo plano contra los tokens que han aparecido al menos una vez. Un valor más alto (p.ej. 1.5) penalizará las repeticiones más fuertemente, mientras que un valor más bajo (p.ej. 0.9) será más indulgente. En 0, está deshabilitado.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Establece un sesgo escalado contra los tokens para penalizar las repeticiones, basado en cuántas veces han aparecido. Un valor más alto (por ejemplo, 1.5) penalizará las repeticiones más fuertemente, mientras que un valor más bajo (por ejemplo, 0.9) será más indulgente. En 0, está deshabilitado.", "Sets how far back for the model to look back to prevent repetition.": "Establece cuántos tokens debe mirar atrás el modelo para prevenir la repetición. ", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Establece la semilla de números aleatorios a usar para la generación. Establecer esto en un número específico hará que el modelo genere el mismo texto para el mismo prompt(prompt).", "Sets the size of the context window used to generate the next token.": "Establece el tamaño de la ventana del contexto utilizada para generar el siguiente token.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Establece las secuencias de parada a usar. Cuando se encuentre este patrón, el LLM dejará de generar texto y retornará. Se pueden establecer varios patrones de parada especificando separadamente múltiples parámetros de parada en un archivo de modelo.", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "¡Ajustes guardados correctamente!", "Share": "Compartir", "Share Chat": "Compartir <PERSON>", "Share to Open WebUI Community": "Compartir con la Comunidad Open-WebUI", "Sharing Permissions": "Permisos al Compartir", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "Accesos cortos con un asterisco (*) depende de la situación y solo activos bajo determinadas condiciones.", "Show": "Mostrar", "Show \"What's New\" modal on login": "Mostrar modal \"Qué hay de Nuevo\" al iniciar sesión", "Show Admin Details in Account Pending Overlay": "Mostrar Detalles Admin en la sobrecapa de 'Cuenta Pendiente'", "Show All": "<PERSON><PERSON>", "Show Less": "<PERSON><PERSON>", "Show Model": "Mostrar <PERSON>", "Show shortcuts": "<PERSON><PERSON>", "Show your support!": "¡Muestra tu apoyo!", "Showcased creativity": "Creatividad exhibida", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "Iniciar <PERSON><PERSON> en {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Iniciar <PERSON><PERSON><PERSON> en {{WEBUI_NAME}} con LDAP", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "<PERSON><PERSON><PERSON> una Cuenta", "Sign up to {{WEBUI_NAME}}": "<PERSON><PERSON>r una Cuenta en {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Iniciando Sesi<PERSON> en {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Skip Cache": "<PERSON><PERSON><PERSON>", "Skip the cache and re-run the inference. Defaults to False.": "Evitar caché y reiniciar la interface. Valor predeterminado Falso", "Sougou Search API sID": "API sID de Sougou Search", "Sougou Search API SK": "SK API de Sougou Search", "Source": "Fuente", "Speech Playback Speed": "Velocidad de Reproducción de Voz", "Speech recognition error: {{error}}": "Error en reconocimiento de voz: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Motor Voz a Texto(STT)", "Stop": "Detener", "Stop Generating": "Detener la Generación", "Stop Sequence": "Secuencia de Parada", "Stream Chat Response": "Transmisión Directa de la  Respuesta del Chat", "Strip Existing OCR": "Descartar OCR Existente", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Descartar OCR existente del PDF y repetirlo. Se ignora si está habilitado Forzar OCR. Valor Predeterminado: Falso", "STT Model": "Modelo STT", "STT Settings": "Ajustes Voz a Texto (STT)", "Stylized PDF Export": "Exportar PDF Estilizado", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (p.ej. sobre el Imperio Romano)", "Success": "Correcto", "Successfully updated.": "Actualizado correctamente.", "Suggested": "Sugerido", "Support": "Soportar", "Support this plugin:": "Apoya este plugin:", "Supported MIME Types": "", "Sync directory": "Sincroniza Directorio", "System": "Sistema", "System Instructions": "Instrucciones del sistema", "System Prompt": "Prompt del sistema", "Tags": "Etiquetas", "Tags Generation": "Generación de Etiquetas", "Tags Generation Prompt": "Prompt para la Generación de Etiquetas", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "El Muestreo de cola libre(TFS_Z) es usado para reducir el impacto de los tokens menos probables en la salida. Un valor más alto (p.ej. 2.0) reduce más fuertemente el impacto, mientras que un valor de 1.0 deshabilita este ajuste.", "Talk to model": "Hablar con el modelo", "Tap to interrupt": "Toca para interrumpir", "Task Model": "<PERSON><PERSON>", "Tasks": "<PERSON><PERSON><PERSON>", "Tavily API Key": "Clave API de Tavily", "Tavily Extract Depth": "Parámetro Extract Depth de Tavi<PERSON>y", "Tell us more:": "Dinos algo más:", "Temperature": "Temperatura", "Temporary Chat": "Chat <PERSON>", "Text Splitter": "Divisor de Texto", "Text-to-Speech": "", "Text-to-Speech Engine": "Motor Texto a Voz(TTS)", "Thanks for your feedback!": "¡<PERSON><PERSON><PERSON> por tu comentario!", "The Application Account DN you bind with for search": "Cuenta DN de la aplicación vinculada para búsqueda", "The base to search for users": "Base para buscar usuarios", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "El tamaño de lote determina cuántas solicitudes de texto se procesan juntas de una vez. Un tamaño de lote más alto puede aumentar el rendimiento y la velocidad del modelo, pero también requiere más memoria.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Quienes desarollaron este complemento son apasionados voluntarios/as de la comunidad. Si este complemento te es útil, por favor considera contribuir a su desarrollo.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "La tabla clasificatoria de evaluación se basa en el sistema de clasificación Elo y se actualiza en tiempo real.", "The format to return a response in. Format can be json or a JSON schema.": "El formato en que se devuelve la respuesta. Puede ser JSON o un esqueema JSON.", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "El atributo LDAP que mapea el correo que los usuarios utilizan para iniciar sesión.", "The LDAP attribute that maps to the username that users use to sign in.": "El atributo LDAP que mapea el nombre de usuario que los usuarios utilizan para iniciar sesión.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "La tabla clasificatoria está actualmente en beta, por lo que los cálculos de clasificación pueden reajustarse a medida que se refina el algoritmo.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "El tamaño máximo del archivo en MB. Si el tamaño del archivo supera este límite, el archivo no se subirá.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "El número máximo de archivos que se pueden utilizar a la vez en el chat. Si se supera este límite, los archivos no se subirán.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "El formato de salida para el texto. <PERSON>uede ser 'json', 'markdown' o 'html'. Valor predeterminado: 'markdown'", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "La puntuación debe ser un valor entre 0.0 (0%) y 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "La temperatura del modelo. Aumentar la temperatura hará que el modelo responda de forma más creativa.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensando...", "This action cannot be undone. Do you wish to continue?": "Esta acción no se puede deshacer. ¿Desea continuar?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Este canal fue creado el {{createdAt}}. Este es el comienzo del canal {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Este chat no aparecerá en el historial y los mensajes no se guardarán.", "This chat won’t appear in history and your messages will not be saved.": "Este chat no aparecerá en el historial y los mensajes no se guardarán.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Esto garantiza que sus valiosas conversaciones se guardan de forma segura en tu base de datos del servidor trasero (backend). ¡Gracias!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Esta es una característica experimental, por lo que puede no funcionar como se esperaba y está sujeta a cambios en cualquier momento.", "This model is not publicly available. Please select another model.": "Este modelo no está disponible publicamente. Por favor, selecciona otro modelo.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "Esta opción controla cuanto tiempo permanece cargado en memoria el modelo tras la petición (por defecto 5m).", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Esta opción controla cuántos tokens se conservan cuando se actualiza el contexto. Por ejemplo, si se establece en 2, se conservarán los primeros 2 tokens del contexto de la conversación. Conservar el contexto puede ayudar a mantener la continuidad de una conversación, pero puede reducir la habilidad para responder a nuevos temas.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Esta opción activa o desactiva el uso de la característica de razonamiento en Ollama, la cuál permite al modelo pensar antes de generar una respuesta. Cuando está activa el modelo se toma un tiempo para procesar el contexto de la conversación y generar una respuesta más razonada.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Esta opción establece el número máximo de tokens que el modelo puede generar en sus respuestas. Aumentar este límite permite al modelo proporcionar respuestas más largas, pero también puede aumentar la probabilidad de que se genere contenido inútil o irrelevante.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Esta opción eliminará todos los archivos existentes en la colección y los reemplazará con los nuevos archivos subidos.", "This response was generated by \"{{model}}\"": "Esta respuesta fue generada por \"{{model}}\"", "This will delete": "Esto eliminará", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Esto eliminará <strong>{{NAME}}</strong> y <strong>todo su contenido</strong>.", "This will delete all models including custom models": "Esto eliminará todos los modelos, incluidos los modelos personalizados", "This will delete all models including custom models and cannot be undone.": "Esto eliminará todos los modelos, incluidos los modelos personalizados y no se puede deshacer.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Esto reinicializará la base de conocimientos y sincronizará todos los archivos. ¿Desea continuar?", "Thorough explanation": "Explicación exhaustiva", "Thought for {{DURATION}}": "<PERSON><PERSON><PERSON> durante {{DURATION}}", "Thought for {{DURATION}} seconds": "<PERSON><PERSON><PERSON> durante {{DURATION}} segundos", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL del Servidor Tika necesaria", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Consejo: Actualiza m<PERSON>les variables encastradas en el chat consecutivamente pulsando la tecla tab en el chat después de cada reemplazo.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (p.ej. cuéntame un hecho divertidado)", "Title Auto-Generation": "AutoGeneración de Títulos", "Title cannot be an empty string.": "El título no puede ser una cadena vacía.", "Title Generation": "Generación de Títulos", "Title Generation Prompt": "Prompt para la Generación de Título", "TLS": "TLS", "To access the available model names for downloading,": "Para acceder a los nombres de modelos disponibles para descargar,", "To access the GGUF models available for downloading,": "Para acceder a los modelos GGUF disponibles para descargar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para acceder a WebUI, por favor contacte con Admins. Los administradores pueden gestionar los estados de los usuarios esde el panel de administración.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Para adjuntar la base de conocimientos aquí, primero añadirla a \"Conocimiento\" en el área de trabajo.", "To learn more about available endpoints, visit our documentation.": "Para aprender más sobre los endpoints disponibles, visite nuestra documentación.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Para proteger tu privacidad, de tus comentarios solo se comparten las calificaciones, IDs de modelo, etiquetas y metadatos; tus chat guardados permanecen privados y no se incluyen.", "To select actions here, add them to the \"Functions\" workspace first.": "Para seleccionar acciones aquí, primero añadirlas a \"Funciones\" en el área de trabajo.", "To select filters here, add them to the \"Functions\" workspace first.": "Para seleccionar filtros aquí, primero añadirlos a \"Funciones\" en el área de trabajo.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Para seleccionar herramientas aquí, primero añadelas a \"Herramientas\" en el área de trabajo.", "Toast notifications for new updates": "Notificaciones emergentes para nuevas actualizaciones", "Today": "Hoy", "Toggle search": "Alternar bus<PERSON>da", "Toggle settings": "<PERSON><PERSON><PERSON>", "Toggle sidebar": "Alternar Barra Lateral", "Toggle whether current connection is active.": "Alternar si la conexión actual está activa", "Token": "Token", "Too verbose": "<PERSON><PERSON><PERSON><PERSON>", "Tool created successfully": "Herramienta creada correctamente", "Tool deleted successfully": "Herramienta eliminada correctamente", "Tool Description": "Descripción de la Herramienta", "Tool ID": "ID de la Herramienta", "Tool imported successfully": "Herramienta importada correctamente", "Tool Name": "Nombre de la Herramienta", "Tool Servers": "Ser<PERSON><PERSON> Herraientas", "Tool updated successfully": "Herramienta actualizada correctamente", "Tools": "Herramientas", "Tools Access": "Acceso a Herramientas", "Tools are a function calling system with arbitrary code execution": "Las herramientas son un sistema de llamada de funciones con ejecución de código arbitrario", "Tools Function Calling Prompt": "Prompt para la Función de Llamada a las Herramientas", "Tools have a function calling system that allows arbitrary code execution.": "Las herramientas tienen un sistema de llamada de funciones que permite la ejecución de código arbitrario.", "Tools Public Sharing": "Compartir Herramientas Publicamente", "Top K": "Top K", "Top K Reranker": "Top K Reclasificador", "Transformers": "Transformadores", "Trouble accessing Ollama?": "¿Problemas para acceder a Ollama?", "Trust Proxy Environment": "Entorno Proxy Confiable", "TTS Model": "Modelo TTS", "TTS Settings": "Ajustes Texto a Voz (TTS)", "TTS Voice": "Voz TTS", "Type": "Tipo", "Type Hugging Face Resolve (Download) URL": "Escribir la URL de Hugging Face Resolve (Descarga)", "Uh-oh! There was an issue with the response.": "¡Vaya! Hubo un problema con la respuesta.", "UI": "IU", "Unarchive All": "<PERSON><PERSON><PERSON><PERSON>", "Unarchive All Archived Chats": "Desarchivar Todos los Chats Archivados", "Unarchive Chat": "Desarchi<PERSON>", "Unloads {{FROM_NOW}}": "Descargas {{FROM_NOW}}", "Unlock mysteries": "Desbloquear misterios", "Unpin": "<PERSON><PERSON><PERSON>", "Unravel secrets": "Desentrañar secretos", "Untagged": "Sin Etiqueta", "Untitled": "<PERSON> Título", "Update": "Actualizar", "Update and Copy Link": "Actualizar y Copiar <PERSON>", "Update for the latest features and improvements.": "Actualizar para las últimas características y mejoras.", "Update password": "Actualizar contraseña", "Updated": "Actualizado", "Updated at": "Actualizado el", "Updated At": "Actualizado El", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Mejore a un plan con licencia para tener capacidades mejoradas, incluyendo personalización de marca e interface, y soporte dedicado.", "Upload": "Subir", "Upload a GGUF model": "Subir un modelo GGUF", "Upload Audio": "Subir Audio", "Upload directory": "Directorio de Subidas", "Upload files": "Subir archivos", "Upload Files": "Subir Archivos", "Upload Pipeline": "Subir Tubería", "Upload Progress": "Progreso de la Subida", "URL": "URL", "URL Mode": "Modo URL", "Usage": "<PERSON><PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "Utilizar '#' en el prompt para cargar e incluir tu conocimiento.", "Use Gravatar": "<PERSON><PERSON>", "Use groups to group your users and assign permissions.": "Usar grupos para agrupar a usuarios y asignar permisos.", "Use Initials": "Usar Iniciales", "Use LLM": "Usar LLM", "Use no proxy to fetch page contents.": "No usar proxy para extraer contenidos", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Usar el proxy asignado en las variables del entorno http_proxy y/o https_proxy para extraer contenido", "user": "usuario", "User": "Usuario", "User location successfully retrieved.": "Ubicación de usuario obtenida correctamente.", "User Webhooks": "<PERSON><PERSON><PERSON>", "Username": "Nombre de Usuario", "Users": "Usuarios", "Using the default arena model with all models. Click the plus button to add custom models.": "Usando el modelo de arena predeterminado con todos los modelos. Pulsar en el botón + para agregar modelos personalizados.", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unidades de tiempo válidas:", "Valves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Valves updated": "Válvulas actualizadas", "Valves updated successfully": "Válvulas actualizados correctamente", "variable": "variable", "variable to have them replaced with clipboard content.": "hace que la variable sea reemplazada con el contenido del portapapeles.", "Verify Connection": "Verificar Conexión", "Verify SSL Certificate": "Verificar Certificado SSL", "Version": "Versión", "Version {{selectedVersion}} of {{totalVersions}}": "Versión {{selectedVersion}} de {{totalVersions}}", "View Replies": "Ver Respuestas", "View Result from **{{NAME}}**": "Ver Resultado desde **{{NAME}}**", "Visibility": "Visibilidad", "Vision": "Visión", "Voice": "Voz", "Voice Input": "Entrada de Voz", "Voice mode": "<PERSON><PERSON>", "Warning": "Aviso", "Warning:": "Aviso:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Aviso: Habilitar esto permitirá a los usuarios subir código arbitrario al servidor.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Aviso: Si actualizas o cambias el modelo de incrustacción, necesitarás re-importar todos los documentos.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Aviso: La ejecución Jupyter habilita la ejecución de código arbitrario, plant<PERSON><PERSON> <PERSON> riesgos de seguridad; Proceder con extrema precaución.", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "Motor Cargador Web", "Web Search": "Búsqueda Web", "Web Search Engine": "Motor Búsqueda Web", "Web Search in Chat": "Búsqueda Web en Chat", "Web Search Query Generation": "Generación de Consulta Búsqueda Web", "Webhook URL": "URL EnganchesWeb(Webhook)", "WebUI Settings": "WebUI Ajustes", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI hará solicitudes a \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI hará solicitudes a \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "Peso de la Recuperación BM25", "What are you trying to achieve?": "¿Qué estás tratando de conseguir?", "What are you working on?": "¿En qué estás trabajando?", "What's New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON>uando está habilitado, el modelo responderá a cada mensaje de chat en tiempo real, generando una respuesta tan pronto como se envíe un mensaje. Este modo es útil para aplicaciones de chat en vivo, pero puede afectar al rendimiento en equipos más lentos.", "wherever you are": "dondequiera que estés", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Al paginar la salida. Cada página será separada por una línea horizontal y número de página. Por defecto: Falso", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Why?": "¿Por qué?", "Widescreen Mode": "<PERSON><PERSON>", "Won": "Ganó", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Trabaja conjuntamente con top-k. Un valor más alto (p.ej. 0.95) dará lugar a un texto más diverso, mientras que un valor más bajo (p.ej. 0.5) generará un texto más centrado y conservador.", "Workspace": "Espacio de Trabajo", "Workspace Permissions": "Permisos del Espacio de Trabajo", "Write": "Escribir", "Write a prompt suggestion (e.g. Who are you?)": "Escribe una sugerencia de prompt (p.ej. ¿quién eres?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escribe un resumen en 50 palabras que resuma [tema o palabra clave].", "Write something...": "Escribe algo...", "Yacy Instance URL": "URL de la instancia Yary", "Yacy Password": "Contraseña de Yacy", "Yacy Username": "Usuario <PERSON>", "Yesterday": "Ayer", "You": "Tu", "You are currently using a trial license. Please contact support to upgrade your license.": "Actualmente estás utilizando una licencia de prueba. Por favor, para actualizar su licencia contacta con soporte.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Solo puedes chatear con un máximo de {{maxCount}} archivo(s) a la vez.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Puedes personalizar tus interacciones con los LLMs añadiendo memorias a través del botón 'Gestionar' debajo, haciendo que te sean más útiles y personalizados.", "You cannot upload an empty file.": "No puedes subir un archivo vacío.", "You do not have permission to upload files.": "No tienes permiso para subir archivos.", "You have no archived conversations.": "No tienes conversaciones archivadas.", "You have shared this chat": "Has compartido esta conversación", "You're a helpful assistant.": "Eres un asistente atento, amable y servicial.", "You're now logged in.": "Has iniciado sesión.", "Your account status is currently pending activation.": "Tu cuenta está pendiente de activación.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Tu entera contribución irá directamente al desarrollador del complemento; Open-WebUI no recibe ningún porcentaje. Sin embargo, la plataforma de financiación elegida podría tener sus propias tarifas.", "Youtube": "Youtube", "Youtube Language": "Youtube Idioma", "Youtube Proxy URL": "Youtube URL Proxy"}