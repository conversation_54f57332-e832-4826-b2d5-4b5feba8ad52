<script lang="ts">
    import { fade } from 'svelte/transition';
    import { createEventDispatcher, onDestroy, onMount } from 'svelte';
    import { flyAndScale } from '$lib/utils/transitions';
    import Spinner from './Spinner.svelte';
    
    const dispatch = createEventDispatcher();
    
    export let show = false;
    export let title = "";
    export let message = "";
    export let size = 'sm'; // sm, md, lg, xl, full
    export let className = '';
    export let containerClassName = '';
    
    let modalElement = null;
    let mounted = false;

    const sizeToWidth = (size: string) => {
        switch (size) {
            case 'sm':
                return 'max-w-md';
            case 'md':
                return 'max-w-lg';
            case 'lg':
                return 'max-w-2xl';
            case 'xl':
                return 'max-w-4xl';
            case 'full':
                return 'max-w-full';
            default:
                return 'max-w-md';
        }
    };

    onMount(() => {
        mounted = true;
    });
    
    $: if (show && modalElement) {
        document.body.appendChild(modalElement);
        document.body.style.overflow = 'hidden';
    } else if (modalElement) {
        try {
            document.body.removeChild(modalElement);
        } catch (e) {}
        document.body.style.overflow = 'unset';
    }
    
    onDestroy(() => {
        show = false;
        
        if (modalElement && document.body.contains(modalElement)) {
            document.body.removeChild(modalElement);
            document.body.style.overflow = 'unset';
        }
    });
</script>

{#if show && mounted}
    <div
        bind:this={modalElement}
        class="modal fixed top-0 right-0 left-0 bottom-0 bg-black/60 w-full h-screen max-h-[100dvh] {containerClassName} flex justify-center z-9999 overflow-y-auto overscroll-contain"
        in:fade={{ duration: 10 }}
    >
        <div
            class="m-auto max-w-full {sizeToWidth(size)} {size !== 'full'
                ? 'mx-2'
                : ''} shadow-3xl min-h-fit scrollbar-hidden {className}"
            in:flyAndScale
        >
            <div class="bg-white dark:bg-gray-900 rounded-xl overflow-hidden">
                <div class="flex flex-col p-5">
                    <!-- Title -->
                    {#if title}
                        <div class="text-lg font-medium text-center mb-3 dark:text-white">{title}</div>
                    {/if}
                    
                    <!-- Spinner -->
                    <div class="flex justify-center my-5">
                        <Spinner className="size-10 text-white" />
                    </div>
                    
                    <!-- Message -->
                    {#if message}
                        <div class="text-center text-gray-600 dark:text-gray-300 mb-3">{message}</div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
{/if}

<style>
    .modal-content {
        animation: scaleUp 0.1s ease-out forwards;
    }

    @keyframes scaleUp {
        from {
            transform: scale(0.985);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }
</style>