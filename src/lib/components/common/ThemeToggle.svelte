<script lang="ts">
	import { theme } from '$lib/stores';
	import Moon from '$lib/components/icons/Moon.svelte';
	import Sun from '$lib/components/icons/Sun.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import { getContext } from 'svelte';

	const i18n = getContext('i18n');

	export let className = '';

	const themeChangeHandler = () => {
		// Toggle between dark and light mode
		const newTheme = $theme.includes('dark') ? 'light' : 'dark';
		theme.set(newTheme);
		localStorage.setItem('theme', newTheme);
		applyTheme(newTheme);
	};

	const applyTheme = (_theme: string) => {
		let themeToApply = _theme === 'oled-dark' ? 'dark' : _theme;

		if (_theme === 'system') {
			themeToApply = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
		}

		if (themeToApply === 'dark' && !_theme.includes('oled')) {
			document.documentElement.style.setProperty('--color-gray-800', '#333');
			document.documentElement.style.setProperty('--color-gray-850', '#262626');
			document.documentElement.style.setProperty('--color-gray-900', '#171717');
			document.documentElement.style.setProperty('--color-gray-950', '#0d0d0d');
		}

		// Remove all theme classes
		document.documentElement.classList.remove('dark', 'light', 'rose-pine', 'rose-pine-dawn', 'oled-dark');

		// Add the new theme class
		themeToApply.split(' ').forEach((e) => {
			document.documentElement.classList.add(e);
		});

		const metaThemeColor = document.querySelector('meta[name="theme-color"]');
		if (metaThemeColor) {
			metaThemeColor.setAttribute(
				'content',
				_theme === 'dark'
					? '#171717'
					: _theme === 'oled-dark'
						? '#000000'
						: _theme === 'her'
							? '#983724'
							: '#ffffff'
			);
		}

		if (typeof window !== 'undefined' && window.applyTheme) {
			window.applyTheme();
		}

		if (_theme.includes('oled')) {
			document.documentElement.style.setProperty('--color-gray-800', '#101010');
			document.documentElement.style.setProperty('--color-gray-850', '#050505');
			document.documentElement.style.setProperty('--color-gray-900', '#000000');
			document.documentElement.style.setProperty('--color-gray-950', '#000000');
			document.documentElement.classList.add('dark');
		}
	};
</script>

<Tooltip content={$i18n.t($theme.includes('dark') ? 'Light mode' : 'Dark mode')}>
	<button
		class="flex items-center gap-1 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors {className}"
		on:click={themeChangeHandler}
		aria-label={$i18n.t('Toggle theme')}
	>
		<div class="self-center">
			{#if $theme.includes('dark')}
				<Sun className="size-5" strokeWidth="1.5" />
			{:else}
				<Moon className="size-5" strokeWidth="1.5" />
			{/if}
		</div>
	</button>
</Tooltip>
