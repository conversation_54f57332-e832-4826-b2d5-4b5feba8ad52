<script lang="ts">
	import { getContext } from 'svelte';
	import { goto } from '$app/navigation';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import { toast } from 'svelte-sonner';
	import Search from '$lib/components/icons/Search.svelte';

	export let query: string;

	const i18n: Writable<i18nType> = getContext('i18n');

	interface SearchResult {
		distances: number[][];
		documents: string[][];
		metadatas: {
			name: string;
			page: number;
			[key: string]: any;
		}[][];
	}

	let searchQuery = query;
	let searchInput: HTMLInputElement;

	let searchResults: SearchResult | null = null;
	let loading = false;

	const fetchSearchResults = async (fetchQuery: string) => {
		loading = true;
		searchResults = null;

		try {
			// TODO: The collection_names should be retrieved dynamically.
			// Using the one from user feedback for now.
			const body = {
				collection_names: ['2f6d8a38-0617-4580-9333-4c071992b1f8'],
				query: fetchQuery,
				k: 2,
				k_reranker: 2,
				r: 0,
				hybrid: true,
				hybrid_bm25_weight: 0
			};

			const response = await fetch(`/api/v1/retrieval/query/collection`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(body)
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
			}

			const data = await response.json();
			searchResults = data;

			if (!data || !data.documents || data.documents.length === 0 || data.documents[0].length === 0) {
				toast.info('No results found for your query.');
			}
		} catch (e) {
			let errorMessage = 'An unknown error occurred.';
			if (e instanceof Error) {
				errorMessage = e.message;
			} else if (typeof e === 'string') {
				errorMessage = e;
			}
			console.error('Failed to fetch search results:', e);
			toast.error(errorMessage);
			searchResults = null;
		} finally {
			loading = false;
		}
	};

	const performSearch = (event?: Event) => {
		if (event) {
			event.preventDefault();
		}
		const queryToSearch = searchQuery.trim();
		if (queryToSearch) {
			goto(`/search?query=${encodeURIComponent(queryToSearch)}`);
		}
	};

	const handleKeyPress = (event: KeyboardEvent) => {
		if (event.key === 'Enter') {
			performSearch(event);
		}
	};

	$: if (query) {
		fetchSearchResults(query);
		searchQuery = query;
	}
</script>

<div class="w-full h-full flex flex-col items-center overflow-y-auto">
	<!-- Search Bar -->
	<div
		class="w-full max-w-3xl px-3 py-4 sticky top-0 bg-white dark:bg-gray-900 z-10 border-b border-gray-200 dark:border-gray-700"
	>
		<form class="w-full relative" on:submit={performSearch}>
			<div class="relative">
				<div class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400">
					<Search />
				</div>
				<input
					bind:this={searchInput}
					bind:value={searchQuery}
					type="text"
					placeholder={$i18n.t('Search...')}
					class="w-full h-11 border border-gray-300 dark:border-gray-600 rounded-full pl-12 pr-4 text-base outline-none hover:shadow-md focus:shadow-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
					autocomplete="off"
					on:keypress={handleKeyPress}
				/>
			</div>
		</form>
	</div>

	<div class="w-full max-w-3xl px-3">
		<!-- Search Results -->
		<div class="space-y-4 mt-4">
			{#if searchResults}
				{#each searchResults.documents[0] as document, i}
					{@const metadata = searchResults.metadatas[0][i]}
					<div class="p-4 border rounded-lg">
						<h3 class="text-lg font-semibold text-blue-600 dark:text-blue-400">
							{metadata.name}
						</h3>
						<p class="text-sm text-gray-500">Page {metadata.page}</p>
						<p class="mt-2 whitespace-pre-wrap">{document}</p>
						<p class="text-xs text-gray-400 mt-2">
							Distance: {searchResults.distances[0][i].toFixed(4)}
						</p>
					</div>
				{/each}
			{:else if loading}
				<p>Loading search results...</p>
			{:else}
				<p>No results found.</p>
			{/if}
		</div>
	</div>
</div>
