<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { goto } from '$app/navigation';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import Search from '$lib/components/icons/Search.svelte';

	const i18n: Writable<i18nType> = getContext('i18n');

	let searchQuery = '';
	let searchInput: HTMLInputElement;

	const performSearch = (event?: Event) => {
		if (event) {
			event.preventDefault();
		}
		const queryToSearch = searchQuery.trim();
		if (queryToSearch) {
			goto(`/search?query=${encodeURIComponent(queryToSearch)}`);
		}
	};

	const handleKeyPress = (event: KeyboardEvent) => {
		if (event.key === 'Enter') {
			performSearch(event);
		}
	};

	onMount(() => {
		searchInput?.focus();
	});
</script>

<div class="w-full h-full flex flex-col justify-center">
	<!-- Search Content -->
	<div class="flex-1 flex items-center justify-center px-5">
		<!-- Search Form -->
		<form class="w-full max-w-3xl relative" on:submit={performSearch}>
			<!-- Logo -->
			<img
				src="/notra.svg"
				alt="Notra"
				class="absolute bottom-full left-1/2 -translate-x-1/2 h-38 mb-2"
			/>

			<div class="relative">
				<div class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400">
					<Search />
				</div>
				<input
					bind:this={searchInput}
					bind:value={searchQuery}
					type="text"
					placeholder={$i18n.t('Search...')}
					class="w-full h-11 border border-gray-300 dark:border-gray-600 rounded-full pl-12 pr-4 text-base outline-none hover:shadow-md focus:shadow-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
					autocomplete="off"
					on:keypress={handleKeyPress}
				/>
			</div>
		</form>
	</div>
</div>
