<!-- filepath: /Users/<USER>/workspace/ai/open-webui/src/lib/components/icons/ViewGrid.svelte -->
<script lang="ts">
	export let className = 'size-5';
	export let strokeWidth = '2';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	width="20"
	height="20"
	viewBox="0 0 24 24"
	fill="none"
	stroke="currentColor"
	stroke-width={strokeWidth}
	stroke-linecap="round"
	stroke-linejoin="round"
	class={className}
>
	<rect x="3" y="3" width="7" height="7"></rect>
	<rect x="14" y="3" width="7" height="7"></rect>
	<rect x="14" y="14" width="7" height="7"></rect>
	<rect x="3" y="14" width="7" height="7"></rect>
</svg>
