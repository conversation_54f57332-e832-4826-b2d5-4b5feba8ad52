import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig, loadEnv } from 'vite';
import { viteStaticCopy } from 'vite-plugin-static-copy';

// /** @type {import('vite').Plugin} */
// const viteServerConfig = {
// 	name: 'log-request-middleware',
// 	configureServer(server) {
// 		server.middlewares.use((req, res, next) => {
// 			res.setHeader('Access-Control-Allow-Origin', '*');
// 			res.setHeader('Access-Control-Allow-Methods', 'GET');
// 			res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
// 			res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
// 			next();
// 		});
// 	}
// };

export default defineConfig(({ mode }) => {
	// 載入環境變數
	const env = loadEnv(mode, process.cwd(), '');
	const WEBUI_BASE_URL = env.VITE_WEBUI_BASE_URL;

	return {
		plugins: [
			sveltekit(),
			viteStaticCopy({
				targets: [
					{
						src: 'node_modules/onnxruntime-web/dist/*.jsep.*',
						dest: 'wasm'
					}
				]
			})
		],
		define: {
			APP_VERSION: JSON.stringify(process.env.npm_package_version),
			APP_BUILD_HASH: JSON.stringify(process.env.APP_BUILD_HASH || 'dev-build')
		},
		server: {
			proxy: {
				// 假設你的API端點是/api開頭
				'/api': {
					target: WEBUI_BASE_URL, // 替換為你的API伺服器地址
					changeOrigin: true,
					secure: false,
					// 如果API不是以/api為前綴的，可以使用rewrite重寫路徑
					// rewrite: (path) => path.replace(/^\/api/, '')
				},
				'/ollama': {
					target: WEBUI_BASE_URL,
					changeOrigin: true,
					secure: false,
				},
				'/openai': {
					target: WEBUI_BASE_URL,
					changeOrigin: true,
					secure: false,
				}
			}
		},
		build: {
			sourcemap: true
		},
		worker: {
			format: 'es'
	},
	esbuild: {
		pure: ['console.log', 'console.debug']
		}
	}
});
